'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Coffee, Shield, Heart, RefreshCw, Play, Pause } from 'lucide-react'
import { PsychologicalProfile } from '@/lib/psychological-service'

interface PsychologicalSafetyProps {
  isVisible: boolean
  onClose: () => void
  onBreakComplete: () => void
  profile?: PsychologicalProfile
  stressLevel?: 'low' | 'medium' | 'high'
}

export default function PsychologicalSafety({ 
  isVisible, 
  onClose, 
  onBreakComplete, 
  profile,
  stressLevel = 'medium'
}: PsychologicalSafetyProps) {
  const [breakType, setBreakType] = useState<'breathing' | 'affirmation' | 'rest'>('breathing')
  const [breakTimer, setBreakTimer] = useState(0)
  const [isBreakActive, setIsBreakActive] = useState(false)
  const [breathingPhase, setBreathingPhase] = useState<'inhale' | 'hold' | 'exhale'>('inhale')

  // Auto-start break when component becomes visible
  useEffect(() => {
    if (isVisible && !isBreakActive) {
      startBreak()
    }
  }, [isVisible])

  const startBreak = () => {
    setIsBreakActive(true)
    setBreakTimer(0)
    
    // Choose break type based on stress level and profile
    if (stressLevel === 'high' || (profile && profile.anxietyLevel === 'high')) {
      setBreakType('breathing')
    } else if (profile && profile.needsEncouragement) {
      setBreakType('affirmation')
    } else {
      setBreakType('rest')
    }
  }

  // Breathing exercise for high stress
  const BreathingBreak = () => {
    const [count, setCount] = useState(0)
    
    useEffect(() => {
      if (!isBreakActive) return
      
      const interval = setInterval(() => {
        setCount(prev => {
          const newCount = prev + 1
          setBreakTimer(newCount)
          
          if (newCount <= 4) {
            setBreathingPhase('inhale')
          } else if (newCount <= 7) {
            setBreathingPhase('hold')
          } else if (newCount <= 11) {
            setBreathingPhase('exhale')
          } else {
            setCount(0)
            return 0
          }
          
          if (newCount >= 60) { // 1 minute break
            setIsBreakActive(false)
            setTimeout(onBreakComplete, 1000)
          }
          
          return newCount
        })
      }, 1000)
      
      return () => clearInterval(interval)
    }, [isBreakActive])

    const getBreathingInstruction = () => {
      switch (breathingPhase) {
        case 'inhale': return 'Tarik napas dalam-dalam...'
        case 'hold': return 'Tahan napas...'
        case 'exhale': return 'Hembuskan perlahan...'
      }
    }

    const getBreathingColor = () => {
      switch (breathingPhase) {
        case 'inhale': return 'from-blue-400 to-blue-600'
        case 'hold': return 'from-yellow-400 to-yellow-600'
        case 'exhale': return 'from-green-400 to-green-600'
      }
    }

    return (
      <div className="text-center space-y-6">
        <div className={`w-32 h-32 mx-auto rounded-full bg-gradient-to-br ${getBreathingColor()} flex items-center justify-center transition-all duration-1000 ${breathingPhase === 'inhale' ? 'scale-110' : breathingPhase === 'exhale' ? 'scale-90' : 'scale-100'}`}>
          <Heart className="h-12 w-12 text-white" />
        </div>
        <div className="space-y-2">
          <p className="text-xl font-medium text-gray-800">{getBreathingInstruction()}</p>
          <p className="text-sm text-gray-500">Sisa waktu: {60 - breakTimer} detik</p>
        </div>
        <Progress value={(breakTimer / 60) * 100} className="w-64 mx-auto" />
      </div>
    )
  }

  // Affirmation break for encouragement
  const AffirmationBreak = () => {
    const affirmations = [
      "Anda sedang melakukan dengan sangat baik",
      "Setiap jawaban Anda menunjukkan kemampuan yang luar biasa",
      "Pengalaman Anda sangat berharga dan unik",
      "Anda memiliki potensi yang besar",
      "Kepercayaan diri Anda terus berkembang",
      "Anda adalah kandidat yang berkualitas"
    ]
    
    const [currentAffirmation, setCurrentAffirmation] = useState(0)
    
    useEffect(() => {
      if (!isBreakActive) return
      
      const interval = setInterval(() => {
        setBreakTimer(prev => {
          const newTimer = prev + 1
          if (newTimer % 5 === 0 && newTimer < 30) {
            setCurrentAffirmation(Math.floor(newTimer / 5) % affirmations.length)
          }
          if (newTimer >= 30) {
            setIsBreakActive(false)
            setTimeout(onBreakComplete, 1000)
          }
          return newTimer
        })
      }, 1000)
      
      return () => clearInterval(interval)
    }, [isBreakActive])

    return (
      <div className="text-center space-y-6">
        <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center">
          <Shield className="h-12 w-12 text-white" />
        </div>
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-800">Pesan Positif untuk Anda</h3>
          <p className="text-lg text-gray-700 font-medium bg-green-50 p-4 rounded-lg">
            {affirmations[currentAffirmation]}
          </p>
          <p className="text-sm text-gray-500">Sisa waktu: {30 - breakTimer} detik</p>
        </div>
        <Progress value={(breakTimer / 30) * 100} className="w-64 mx-auto" />
      </div>
    )
  }

  // Rest break for general relaxation
  const RestBreak = () => {
    useEffect(() => {
      if (!isBreakActive) return
      
      const interval = setInterval(() => {
        setBreakTimer(prev => {
          const newTimer = prev + 1
          if (newTimer >= 20) {
            setIsBreakActive(false)
            setTimeout(onBreakComplete, 1000)
          }
          return newTimer
        })
      }, 1000)
      
      return () => clearInterval(interval)
    }, [isBreakActive])

    return (
      <div className="text-center space-y-6">
        <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center">
          <Coffee className="h-12 w-12 text-white" />
        </div>
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-800">Waktu Istirahat Sejenak</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Ambil waktu sejenak untuk rileks. Anda sudah melakukan dengan baik sejauh ini.
          </p>
          <p className="text-sm text-gray-500">Sisa waktu: {20 - breakTimer} detik</p>
        </div>
        <Progress value={(breakTimer / 20) * 100} className="w-64 mx-auto" />
      </div>
    )
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl">
        <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-center">
          <CardTitle className="text-2xl font-bold">Psychological Safety Break</CardTitle>
          <p className="text-blue-100">Mari ambil waktu sejenak untuk diri Anda</p>
        </CardHeader>
        <CardContent className="p-8">
          {breakType === 'breathing' && <BreathingBreak />}
          {breakType === 'affirmation' && <AffirmationBreak />}
          {breakType === 'rest' && <RestBreak />}
          
          <div className="mt-8 text-center">
            <Button 
              variant="outline" 
              onClick={onClose}
              className="mr-4"
            >
              Lewati Break
            </Button>
            <Button 
              onClick={() => {
                setIsBreakActive(false)
                onBreakComplete()
              }}
              disabled={isBreakActive}
              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
            >
              {isBreakActive ? 'Sedang Break...' : 'Lanjutkan Interview'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

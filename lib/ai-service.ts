import { GoogleGenerativeAI } from "@google/generative-ai"

// Function to process user input with Gemini AI
export async function processUserInput(userInput: string): Promise<string> {
  try {
    console.log("Processing user input with Gemini AI...")

    // Get the API key from environment variables
    const geminiApiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY
    if (!geminiApiKey) {
      console.error("No Gemini API key found in environment variables")
      throw new Error("Gemini API key not configured")
    }

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(geminiApiKey)
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" })

    const prompt = `<PERSON>a adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.
    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.
    Jaga agar respons <PERSON>a singkat, profesional, dan menarik.
    Gunakan bahasa Indonesia yang baik dan benar.

    Respons kandidat: "${userInput}"

    Respons Anda sebagai interviewer:`

    console.log("Sending request to Gemini AI...")
    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    console.log("Received response from Gemini AI")
    return text
  } catch (error) {
    console.error("Error processing with Gemini AI:", error)
    // Return a more specific error message if possible
    if (error instanceof Error) {
      return `Maaf, terjadi kesalahan dalam memproses respons: ${error.message}. Bisakah Anda mengulangi jawaban Anda?`
    }
    return "Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?"
  }
}

// Function to convert speech to text using Web Speech API (browser-based)
export function speechToText(): Promise<string> {
  return new Promise((resolve, reject) => {
    if (typeof window === "undefined" || !("webkitSpeechRecognition" in window)) {
      reject(new Error("Speech recognition not supported in this browser"))
      return
    }

    // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition
    const recognition = new window.webkitSpeechRecognition()
    recognition.lang = "id-ID" // Indonesian language
    recognition.interimResults = false
    recognition.maxAlternatives = 1

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript
      resolve(transcript)
    }

    recognition.onerror = (event: any) => {
      reject(new Error(`Speech recognition error: ${event.error}`))
    }

    recognition.start()
  })
}

// Voice options for Gemini TTS
export const VOICE_OPTIONS = {
  female: {
    id: "female-1", // Gemini female voice
    name: "Gemini Female",
    description: "Suara perempuan yang natural dari Gemini AI"
  },
  male: {
    id: "male-1", // Gemini male voice
    name: "Gemini Male",
    description: "Suara laki-laki yang natural dari Gemini AI"
  }
} as const

export type VoiceGender = keyof typeof VOICE_OPTIONS

// Function to fetch available voices from Google TTS API
export async function fetchAvailableVoices(): Promise<any> {
  try {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY
    if (!apiKey) {
      throw new Error("Gemini API key not configured")
    }

    const response = await fetch(`https://texttospeech.googleapis.com/v1/voices?key=${apiKey}`)
    const data = await response.json()

    // Filter Indonesian voices
    const indonesianVoices = data.voices?.filter((voice: any) =>
      voice.languageCodes.includes("id-ID")
    ) || []

    console.log("Available Indonesian voices:", indonesianVoices)
    return indonesianVoices
  } catch (error) {
    console.error("Error fetching voices:", error)
    return []
  }
}

// Function to convert text to speech using Gemini TTS API with voice selection
export async function geminiTextToSpeech(text: string, voiceGender: VoiceGender = 'female'): Promise<ArrayBuffer> {
  try {
    console.log(`Starting text-to-speech with Gemini API using ${voiceGender} voice...`)

    // Get the API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY
    if (!apiKey) {
      console.error("No Gemini API key found in environment variables")
      throw new Error("Gemini API key not configured")
    }

    // Get the voice configuration based on gender selection
    const voiceConfig = VOICE_OPTIONS[voiceGender]
    console.log(`Using voice: ${voiceConfig.name}`)
    console.log(`Selected gender: ${voiceGender}`)

    // Prepare the request body for Gemini TTS
    const requestBody = {
      input: {
        text: text
      },
      voice: {
        languageCode: "id-ID", // Indonesian language
        name: voiceGender === 'female' ? "id-ID-Standard-A" : "id-ID-Standard-C", // Female vs Male voice
        ssmlGender: voiceGender === 'female' ? "FEMALE" : "MALE"
      },
      audioConfig: {
        audioEncoding: "MP3",
        speakingRate: 1.0,
        pitch: 0.0,
        volumeGainDb: 0.0
      }
    }

    console.log("Sending text to Gemini TTS API...")
    console.log("Request body:", JSON.stringify(requestBody, null, 2))

    // Make the API request to Google Cloud Text-to-Speech (Gemini)
    const response = await fetch(`https://texttospeech.googleapis.com/v1/text:synthesize?key=${apiKey}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(requestBody)
    })

    console.log(`Received response from Gemini TTS API with status: ${response.status}`)

    if (!response.ok) {
      let errorMessage = response.statusText
      try {
        const errorData = await response.json()
        console.error("TTS API Error Data:", errorData)
        errorMessage = errorData.error?.message || errorMessage
      } catch (e) {
        console.error("Failed to parse TTS API error response", e)
      }
      throw new Error(`Gemini TTS API error (${response.status}): ${errorMessage}`)
    }

    const data = await response.json()

    // The response contains base64 encoded audio
    if (!data.audioContent) {
      throw new Error("No audio content received from Gemini TTS API")
    }

    // Convert base64 to ArrayBuffer
    const audioData = Uint8Array.from(atob(data.audioContent), c => c.charCodeAt(0)).buffer
    console.log("Successfully generated speech audio with Gemini TTS")
    return audioData
  } catch (error) {
    console.error("Error generating speech with Gemini TTS:", error)
    throw new Error(`Failed to generate speech with Gemini TTS API: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Function to convert text to speech with voice gender selection
export async function textToSpeech(text: string, voiceGender: VoiceGender = 'female'): Promise<string> {
  try {
    // Use Gemini TTS API to generate speech (primary)
    const audioData = await geminiTextToSpeech(text, voiceGender)

    // Convert the ArrayBuffer to a Blob
    const audioBlob = new Blob([audioData], { type: 'audio/mp3' })

    // Create a URL for the audio blob
    const audioUrl = URL.createObjectURL(audioBlob)

    // Play the audio
    const audio = new Audio(audioUrl)
    audio.play()

    // Return the URL so it can be stored if needed
    return audioUrl
  } catch (error) {
    console.error("Error with Gemini TTS, using browser TTS fallback:", error)

    // Fall back to browser's built-in TTS if ElevenLabs fails
    return new Promise((resolve, reject) => {
      if (typeof window === "undefined" || !("speechSynthesis" in window)) {
        reject(new Error("Text-to-speech not supported in this browser"))
        return
      }

      // Create a speech utterance
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = "id-ID" // Indonesian language

      // Try to find an Indonesian voice based on gender preference
      const voices = window.speechSynthesis.getVoices()
      let selectedVoice = voices.find((voice) => voice.lang.includes("id-ID"))

      // If no Indonesian voice, try to find gender-appropriate voice
      if (!selectedVoice) {
        if (voiceGender === 'female') {
          selectedVoice = voices.find((voice) => voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'))
        } else {
          selectedVoice = voices.find((voice) => voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'))
        }
      }

      if (selectedVoice) {
        utterance.voice = selectedVoice
      }

      // Play the speech
      window.speechSynthesis.speak(utterance)
      resolve("")
    })
  }
}

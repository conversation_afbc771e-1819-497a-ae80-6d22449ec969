import { GoogleGenerativeAI } from "@google/generative-ai"

// Function to process user input with Gemini AI
export async function processUserInput(userInput: string): Promise<string> {
  try {
    console.log("Processing user input with Gemini AI...")

    // Get the API key from environment variables
    const geminiApiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY
    if (!geminiApiKey) {
      console.error("No Gemini API key found in environment variables")
      throw new Error("Gemini API key not configured")
    }

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(geminiApiKey)
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" })

    const prompt = `<PERSON>a adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.
    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.
    Jaga agar respons <PERSON>a singkat, profesional, dan menarik.
    Gunakan bahasa Indonesia yang baik dan benar.

    Respons kandidat: "${userInput}"

    Respons Anda sebagai interviewer:`

    console.log("Sending request to Gemini AI...")
    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    console.log("Received response from Gemini AI")
    return text
  } catch (error) {
    console.error("Error processing with Gemini AI:", error)
    // Return a more specific error message if possible
    if (error instanceof Error) {
      return `Maaf, terjadi kesalahan dalam memproses respons: ${error.message}. Bisakah Anda mengulangi jawaban Anda?`
    }
    return "Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?"
  }
}

// Function to convert speech to text using Web Speech API (browser-based)
export function speechToText(): Promise<string> {
  return new Promise((resolve, reject) => {
    if (typeof window === "undefined" || !("webkitSpeechRecognition" in window)) {
      reject(new Error("Speech recognition not supported in this browser"))
      return
    }

    // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition
    const recognition = new window.webkitSpeechRecognition()
    recognition.lang = "id-ID" // Indonesian language
    recognition.interimResults = false
    recognition.maxAlternatives = 1

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript
      resolve(transcript)
    }

    recognition.onerror = (event: any) => {
      reject(new Error(`Speech recognition error: ${event.error}`))
    }

    recognition.start()
  })
}

// Voice options for ElevenLabs
export const VOICE_OPTIONS = {
  female: {
    id: "21m00Tcm4TlvDq8ikWAM", // Rachel - Natural female voice
    name: "Rachel (Perempuan)",
    description: "Suara perempuan yang natural dan profesional"
  },
  male: {
    id: "pNInz6obpgDQGcFmaJgB", // Adam - Deep male voice
    name: "Adam (Laki-laki)",
    description: "Suara laki-laki yang dalam dan tegas"
  }
} as const

export type VoiceGender = keyof typeof VOICE_OPTIONS

// Function to convert text to speech using ElevenLabs API with voice selection
export async function elevenLabsTextToSpeech(text: string, voiceGender: VoiceGender = 'female'): Promise<ArrayBuffer> {
  try {
    console.log(`Starting text-to-speech with ElevenLabs API using ${voiceGender} voice...`)

    // Get the API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || process.env.ELEVENLABS_API_KEY
    if (!apiKey) {
      console.error("No ElevenLabs API key found in environment variables")
      throw new Error("ElevenLabs API key not configured")
    }

    // Get the voice ID based on gender selection
    const voiceId = VOICE_OPTIONS[voiceGender].id
    console.log(`Using voice: ${VOICE_OPTIONS[voiceGender].name}`)
    console.log(`Voice ID: ${voiceId}`)
    console.log(`Selected gender: ${voiceGender}`)

    // Prepare the request body
    const requestBody = {
      text: text,
      model_id: "eleven_multilingual_v2", // Supports multiple languages including Indonesian
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5,
        style: 0.0,
        use_speaker_boost: true
      }
    }

    console.log("Sending text to ElevenLabs API...")
    console.log("Request body:", JSON.stringify(requestBody, null, 2))

    // Make the API request to ElevenLabs
    const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
      method: "POST",
      headers: {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": apiKey
      },
      body: JSON.stringify(requestBody)
    })

    console.log(`Received response from ElevenLabs API with status: ${response.status}`)

    if (!response.ok) {
      let errorMessage = response.statusText
      try {
        const errorData = await response.json()
        console.error("ElevenLabs API Error Data:", errorData)
        errorMessage = errorData.detail?.message || errorData.message || errorMessage
      } catch (e) {
        console.error("Failed to parse ElevenLabs API error response", e)
      }
      throw new Error(`ElevenLabs API error (${response.status}): ${errorMessage}`)
    }

    // Get the audio data as ArrayBuffer
    const audioData = await response.arrayBuffer()
    console.log("Successfully generated speech audio with ElevenLabs")
    return audioData
  } catch (error) {
    console.error("Error generating speech with ElevenLabs:", error)
    throw new Error(`Failed to generate speech with ElevenLabs API: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Function to convert text to speech with voice gender selection
export async function textToSpeech(text: string, voiceGender: VoiceGender = 'female'): Promise<string> {
  try {
    // Use ElevenLabs API to generate speech (primary)
    const audioData = await elevenLabsTextToSpeech(text, voiceGender)

    // Convert the ArrayBuffer to a Blob
    const audioBlob = new Blob([audioData], { type: 'audio/mp3' })

    // Create a URL for the audio blob
    const audioUrl = URL.createObjectURL(audioBlob)

    // Play the audio
    const audio = new Audio(audioUrl)
    audio.play()

    // Return the URL so it can be stored if needed
    return audioUrl
  } catch (error) {
    console.error("Error with ElevenLabs TTS, using browser TTS fallback:", error)

    // Fall back to browser's built-in TTS if ElevenLabs fails
    return new Promise((resolve, reject) => {
      if (typeof window === "undefined" || !("speechSynthesis" in window)) {
        reject(new Error("Text-to-speech not supported in this browser"))
        return
      }

      // Create a speech utterance
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = "id-ID" // Indonesian language

      // Try to find an Indonesian voice based on gender preference
      const voices = window.speechSynthesis.getVoices()
      let selectedVoice = voices.find((voice) => voice.lang.includes("id-ID"))

      // If no Indonesian voice, try to find gender-appropriate voice
      if (!selectedVoice) {
        if (voiceGender === 'female') {
          selectedVoice = voices.find((voice) => voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'))
        } else {
          selectedVoice = voices.find((voice) => voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'))
        }
      }

      if (selectedVoice) {
        utterance.voice = selectedVoice
      }

      // Play the speech
      window.speechSynthesis.speak(utterance)
      resolve("")
    })
  }
}

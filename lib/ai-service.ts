import { groq } from "@ai-sdk/groq"
import { generateText } from "ai"

// Function to process user input with Groq
export async function processUserInput(userInput: string): Promise<string> {
  try {
    console.log("Processing user input with Groq API...")

    // Get the API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY || process.env.GROQ_API_KEY
    if (!apiKey) {
      console.error("No Groq API key found in environment variables")
      throw new Error("API key not configured")
    }

    // Set the API key in the environment for the Groq client to use
    if (typeof process !== 'undefined') {
      process.env.GROQ_API_KEY = apiKey
    }

    // Create the Groq client (it will automatically use the GROQ_API_KEY from env)
    const groqModel = groq("llama-3.1-8b-instant")

    console.log("Sending request to Groq API...")
    const { text } = await generateText({
      model: groqModel,
      prompt: `You are an AI interviewer conducting a job interview in Indonesian language.
      You should ask relevant follow-up questions based on the candidate's responses.
      Keep your responses concise, professional, and engaging.

      Candidate's response: "${userInput}"

      Your response as the interviewer:`,
    })

    console.log("Received response from Groq API")
    return text
  } catch (error) {
    console.error("Error processing with Groq:", error)
    // Return a more specific error message if possible
    if (error instanceof Error) {
      return `Maaf, terjadi kesalahan dalam memproses respons: ${error.message}. Bisakah Anda mengulangi jawaban Anda?`
    }
    return "Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?"
  }
}

// Function to convert speech to text using Web Speech API (browser-based)
export function browserSpeechToText(): Promise<string> {
  return new Promise((resolve, reject) => {
    if (typeof window === "undefined" || !("webkitSpeechRecognition" in window)) {
      reject(new Error("Speech recognition not supported in this browser"))
      return
    }

    // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition
    const recognition = new window.webkitSpeechRecognition()
    recognition.lang = "id-ID" // Indonesian language
    recognition.interimResults = false
    recognition.maxAlternatives = 1

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript
      resolve(transcript)
    }

    recognition.onerror = (event: any) => {
      reject(new Error(`Speech recognition error: ${event.error}`))
    }

    recognition.start()
  })
}

// Function to convert speech to text using Groq's Whisper API
export async function groqSpeechToText(audioBlob: Blob): Promise<string> {
  try {
    console.log("Starting transcription with Groq Whisper API...")

    // Get the API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY || process.env.GROQ_API_KEY
    if (!apiKey) {
      console.error("No Groq API key found in environment variables")
      throw new Error("API key not configured")
    }

    // Create a FormData object to send the audio file
    const formData = new FormData()

    // Determine the file extension based on the audio blob type
    let fileExtension = 'webm'
    if (audioBlob.type) {
      const mimeType = audioBlob.type.split('/')[1]
      if (mimeType) {
        fileExtension = mimeType
      }
    }

    formData.append("file", audioBlob, `recording.${fileExtension}`)
    formData.append("model", "whisper-large-v3")
    formData.append("language", "id") // Indonesian language code (ISO-639-1)

    console.log(`Sending audio file (${fileExtension}) to Groq API...`)

    // Make the API request to Groq
    const response = await fetch("https://api.groq.com/openai/v1/audio/transcriptions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
      body: formData,
    })

    console.log(`Received response from Groq API with status: ${response.status}`)

    if (!response.ok) {
      let errorMessage = response.statusText
      try {
        const errorData = await response.json()
        errorMessage = errorData.error?.message || errorMessage
      } catch (e) {
        // If parsing JSON fails, use the status text
      }
      throw new Error(`Groq API error: ${errorMessage}`)
    }

    const data = await response.json()
    console.log("Successfully transcribed audio")
    return data.text
  } catch (error) {
    console.error("Error transcribing with Groq:", error)
    throw new Error(`Failed to transcribe audio with Groq API: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Main speech to text function that uses Groq's Whisper API
export async function speechToText(audioBlob?: Blob): Promise<string> {
  try {
    // If an audio blob is provided, use Groq's API
    if (audioBlob) {
      return await groqSpeechToText(audioBlob)
    }

    // Otherwise, fall back to browser-based speech recognition
    return await browserSpeechToText()
  } catch (error) {
    console.error("Speech to text error:", error)
    throw error
  }
}

// Function to convert text to speech using Groq's PlayAI TTS API
export async function groqTextToSpeech(text: string): Promise<ArrayBuffer> {
  try {
    console.log("Starting text-to-speech with Groq PlayAI TTS API...")

    // Get the API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY || process.env.GROQ_API_KEY
    if (!apiKey) {
      console.error("No Groq API key found in environment variables")
      throw new Error("API key not configured")
    }

    // Prepare the request body
    const requestBody = {
      model: "playai-tts", // Using the PlayAI TTS model
      input: text,
      voice: "alloy", // Default voice, can be customized
      speed: 1.0, // Default speed, can be customized
      response_format: "mp3" // Output format
    }

    console.log("Sending text to Groq TTS API...")

    // Make the API request to Groq
    const response = await fetch("https://api.groq.com/openai/v1/audio/speech", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(requestBody)
    })

    console.log(`Received response from Groq TTS API with status: ${response.status}`)

    if (!response.ok) {
      let errorMessage = response.statusText
      try {
        const errorData = await response.json()
        errorMessage = errorData.error?.message || errorMessage
      } catch (e) {
        // If parsing JSON fails, use the status text
      }
      throw new Error(`Groq TTS API error: ${errorMessage}`)
    }

    // Get the audio data as ArrayBuffer
    const audioData = await response.arrayBuffer()
    console.log("Successfully generated speech audio")
    return audioData
  } catch (error) {
    console.error("Error generating speech with Groq:", error)
    throw new Error(`Failed to generate speech with Groq API: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Function to convert text to speech
export async function textToSpeech(text: string): Promise<string> {
  try {
    // Use Groq's PlayAI TTS API to generate speech
    const audioData = await groqTextToSpeech(text)

    // Convert the ArrayBuffer to a Blob
    const audioBlob = new Blob([audioData], { type: 'audio/mp3' })

    // Create a URL for the audio blob
    const audioUrl = URL.createObjectURL(audioBlob)

    // Play the audio
    const audio = new Audio(audioUrl)
    audio.play()

    // Return the URL so it can be stored if needed
    return audioUrl
  } catch (error) {
    console.error("Error in text-to-speech:", error)

    // Fall back to browser's built-in TTS if Groq API fails
    return new Promise((resolve, reject) => {
      if (typeof window === "undefined" || !("speechSynthesis" in window)) {
        reject(new Error("Text-to-speech not supported in this browser"))
        return
      }

      // Create a speech utterance
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = "id-ID" // Indonesian language

      // Try to find an Indonesian voice
      const voices = window.speechSynthesis.getVoices()
      const indonesianVoice = voices.find((voice) => voice.lang.includes("id-ID"))
      if (indonesianVoice) {
        utterance.voice = indonesianVoice
      }

      // Play the speech
      window.speechSynthesis.speak(utterance)
      resolve("")
    })
  }
}

// Psychological Assessment and Comfort Features for AI Interview
// Menangani aspek psikologis yang tidak kasat mata namun sangat penting

export type AnxietyLevel = 'low' | 'moderate' | 'high'
export type CommunicationStyle = 'direct' | 'gentle' | 'encouraging' | 'formal'
export type PersonalityType = 'introvert' | 'extrovert' | 'ambivert'

export interface PsychologicalProfile {
  anxietyLevel: AnxietyLevel
  communicationStyle: CommunicationStyle
  personalityType: PersonalityType
  preferredPace: 'slow' | 'normal' | 'fast'
  needsEncouragement: boolean
  culturalBackground: string
  languageConfidence: 'low' | 'medium' | 'high'
  previousInterviewExperience: boolean
}

export interface StressIndicators {
  speechRate: number // words per minute
  pauseFrequency: number // pauses per minute
  voiceShaking: boolean
  repetitiveWords: number
  confidenceLevel: number // 0-100
}

// Pre-interview psychological assessment questions
export const PSYCHOLOGICAL_ASSESSMENT_QUESTIONS = [
  {
    id: 'anxiety_level',
    question: 'Bagaimana perasaan Anda saat ini menjelang wawancara?',
    options: [
      { value: 'low', label: 'Tenang dan siap', weight: 1 },
      { value: 'moderate', label: 'Sedikit nervous tapi optimis', weight: 2 },
      { value: 'high', label: 'Sangat nervous dan khawatir', weight: 3 }
    ]
  },
  {
    id: 'communication_preference',
    question: 'Gaya komunikasi seperti apa yang membuat Anda nyaman?',
    options: [
      { value: 'direct', label: 'Langsung to the point', weight: 1 },
      { value: 'gentle', label: 'Lembut dan bertahap', weight: 2 },
      { value: 'encouraging', label: 'Penuh semangat dan motivasi', weight: 3 },
      { value: 'formal', label: 'Formal dan profesional', weight: 4 }
    ]
  },
  {
    id: 'personality_type',
    question: 'Dalam situasi sosial, Anda lebih suka:',
    options: [
      { value: 'introvert', label: 'Mendengarkan dan berpikir dulu sebelum bicara', weight: 1 },
      { value: 'extrovert', label: 'Langsung berbicara dan berinteraksi aktif', weight: 2 },
      { value: 'ambivert', label: 'Tergantung situasi dan mood', weight: 3 }
    ]
  },
  {
    id: 'interview_experience',
    question: 'Apakah Anda pernah mengikuti wawancara kerja sebelumnya?',
    options: [
      { value: 'experienced', label: 'Ya, sudah sering', weight: 1 },
      { value: 'some', label: 'Beberapa kali', weight: 2 },
      { value: 'first_time', label: 'Ini pertama kali', weight: 3 }
    ]
  },
  {
    id: 'language_confidence',
    question: 'Seberapa percaya diri Anda berbicara dalam wawancara?',
    options: [
      { value: 'high', label: 'Sangat percaya diri', weight: 1 },
      { value: 'medium', label: 'Cukup percaya diri', weight: 2 },
      { value: 'low', label: 'Kurang percaya diri', weight: 3 }
    ]
  }
]

// Analyze psychological profile from assessment answers
export function analyzePsychologicalProfile(answers: Record<string, string>): PsychologicalProfile {
  const anxietyLevel = answers.anxiety_level as AnxietyLevel || 'moderate'
  const communicationStyle = answers.communication_preference as CommunicationStyle || 'gentle'
  const personalityType = answers.personality_type as PersonalityType || 'ambivert'
  
  // Determine preferred pace based on anxiety and personality
  let preferredPace: 'slow' | 'normal' | 'fast' = 'normal'
  if (anxietyLevel === 'high' || personalityType === 'introvert') {
    preferredPace = 'slow'
  } else if (anxietyLevel === 'low' && personalityType === 'extrovert') {
    preferredPace = 'fast'
  }

  // Determine if needs encouragement
  const needsEncouragement = anxietyLevel === 'high' || 
                           answers.interview_experience === 'first_time' ||
                           answers.language_confidence === 'low'

  return {
    anxietyLevel,
    communicationStyle,
    personalityType,
    preferredPace,
    needsEncouragement,
    culturalBackground: 'indonesian', // Default, bisa diperluas
    languageConfidence: answers.language_confidence as 'low' | 'medium' | 'high' || 'medium',
    previousInterviewExperience: answers.interview_experience !== 'first_time'
  }
}

// Generate adaptive prompts based on psychological profile
export function generateAdaptivePrompts(profile: PsychologicalProfile): {
  systemPrompt: string
  welcomeMessage: string
  encouragementMessages: string[]
  paceInstructions: string
} {
  let systemPrompt = `Anda adalah AI interviewer yang sangat memahami psikologi kandidat. `
  let welcomeMessage = ''
  let encouragementMessages: string[] = []
  let paceInstructions = ''

  // Adapt based on anxiety level
  switch (profile.anxietyLevel) {
    case 'high':
      systemPrompt += `Kandidat memiliki tingkat kecemasan tinggi. Gunakan nada yang sangat menenangkan, berikan banyak positive reinforcement, dan jangan terburu-buru. `
      welcomeMessage = `Halo! Saya senang sekali bisa berbicara dengan Anda hari ini. Tidak perlu khawatir, ini adalah percakapan santai untuk saling mengenal. Tarik napas dalam-dalam, dan mari kita mulai dengan rileks. 😊`
      encouragementMessages = [
        "Jawaban Anda sangat bagus! Anda melakukannya dengan baik.",
        "Saya bisa merasakan antusiasme Anda. Terus seperti itu!",
        "Tidak apa-apa jika perlu waktu untuk berpikir. Saya akan menunggu.",
        "Anda sudah menunjukkan kemampuan yang luar biasa sejauh ini."
      ]
      paceInstructions = `Berikan jeda 3-5 detik setelah setiap pertanyaan. Gunakan kalimat pendek dan jelas.`
      break

    case 'moderate':
      systemPrompt += `Kandidat memiliki tingkat kecemasan sedang. Berikan dukungan yang seimbang dan maintain positive energy. `
      welcomeMessage = `Halo! Senang bertemu dengan Anda. Saya yakin ini akan menjadi percakapan yang menyenangkan. Mari kita mulai! 🌟`
      encouragementMessages = [
        "Bagus sekali! Anda menjelaskan dengan sangat jelas.",
        "Saya suka cara Anda berpikir tentang hal ini.",
        "Pengalaman yang Anda ceritakan sangat menarik.",
        "Anda memiliki perspektif yang unik dan berharga."
      ]
      paceInstructions = `Gunakan pace normal dengan sesekali memberikan positive feedback.`
      break

    case 'low':
      systemPrompt += `Kandidat terlihat percaya diri. Anda bisa lebih direct dan challenging dalam pertanyaan. `
      welcomeMessage = `Halo! Saya siap untuk mendengar cerita menarik dari Anda. Mari kita mulai wawancara ini! 🚀`
      encouragementMessages = [
        "Excellent! Jawaban yang sangat komprehensif.",
        "Saya terkesan dengan pengalaman Anda.",
        "Anda memiliki pemahaman yang mendalam tentang topik ini.",
        "Leadership quality Anda terlihat jelas dari cerita tadi."
      ]
      paceInstructions = `Gunakan pace yang lebih cepat dan pertanyaan yang lebih menantang.`
      break
  }

  // Adapt based on communication style
  switch (profile.communicationStyle) {
    case 'direct':
      systemPrompt += `Kandidat menyukai komunikasi langsung. Gunakan pertanyaan yang to the point tanpa basa-basi berlebihan. `
      break
    case 'gentle':
      systemPrompt += `Kandidat menyukai komunikasi yang lembut. Gunakan pendekatan yang soft dan bertahap. `
      break
    case 'encouraging':
      systemPrompt += `Kandidat membutuhkan banyak motivasi. Berikan banyak positive reinforcement dan semangat. `
      break
    case 'formal':
      systemPrompt += `Kandidat menyukai komunikasi formal. Gunakan bahasa yang profesional dan struktur yang jelas. `
      break
  }

  // Adapt based on personality type
  switch (profile.personalityType) {
    case 'introvert':
      systemPrompt += `Kandidat adalah introvert. Berikan waktu untuk berpikir, jangan interrupt, dan hargai kedalaman jawaban mereka. `
      paceInstructions += ` Berikan extra time untuk reflection.`
      break
    case 'extrovert':
      systemPrompt += `Kandidat adalah extrovert. Mereka suka berinteraksi aktif, jadi berikan energy yang matching dan follow-up questions. `
      break
    case 'ambivert':
      systemPrompt += `Kandidat adalah ambivert. Adaptasi dengan situasi dan baca cues dari respons mereka. `
      break
  }

  // Add language confidence adaptation
  if (profile.languageConfidence === 'low') {
    systemPrompt += `Kandidat kurang percaya diri dengan bahasa. Gunakan bahasa yang sederhana, berikan waktu extra, dan jangan fokus pada grammar mistakes. Fokus pada konten, bukan pada kesempurnaan bahasa. `
    encouragementMessages.push("Bahasa Anda sudah sangat baik dan mudah dipahami.")
    encouragementMessages.push("Yang penting adalah ide dan pengalaman Anda, bukan kesempurnaan bahasa.")
  }

  // Add cultural sensitivity for Indonesian context
  if (profile.culturalBackground === 'indonesian') {
    systemPrompt += `Kandidat adalah orang Indonesia. Gunakan pendekatan yang sesuai dengan budaya Indonesia:
    - Hargai nilai kesopanan dan kerendahan hati
    - Jangan terlalu direct jika kandidat terlihat tidak nyaman
    - Berikan apresiasi untuk pencapaian sekecil apapun
    - Gunakan bahasa yang hangat dan bersahabat
    - Pahami bahwa kandidat mungkin tidak suka terlalu menonjolkan diri (budaya tidak sombong)
    `

    // Add Indonesian-specific encouragement
    encouragementMessages.push("Pengalaman yang Anda bagikan sangat berharga.")
    encouragementMessages.push("Saya bisa merasakan dedikasi Anda dalam bekerja.")
    encouragementMessages.push("Terima kasih sudah berbagi dengan jujur dan terbuka.")
  }

  // Add first-time interview adaptation
  if (!profile.previousInterviewExperience) {
    systemPrompt += `Ini adalah pengalaman wawancara pertama kandidat. Berikan guidance dan explain process dengan jelas. `
    welcomeMessage += ` Karena ini mungkin pengalaman wawancara pertama Anda, saya akan memandu Anda step by step. Tidak perlu khawatir!`
  }

  return {
    systemPrompt,
    welcomeMessage,
    encouragementMessages,
    paceInstructions
  }
}

// Detect stress indicators from speech patterns (placeholder for future implementation)
export function analyzeStressIndicators(audioData: ArrayBuffer): Promise<StressIndicators> {
  // This would integrate with speech analysis APIs in the future
  // For now, return mock data based on realistic patterns
  return Promise.resolve({
    speechRate: 150, // normal range 140-180 wpm
    pauseFrequency: 8, // normal range 6-12 per minute
    voiceShaking: false,
    repetitiveWords: 2, // normal range 1-3
    confidenceLevel: 75 // 0-100 scale
  })
}

// Generate real-time encouragement based on stress indicators
export function generateRealTimeEncouragement(
  stressIndicators: StressIndicators,
  profile: PsychologicalProfile
): string | null {
  if (stressIndicators.confidenceLevel < 50) {
    if (profile.needsEncouragement) {
      return "Anda sedang melakukan dengan baik. Tarik napas dalam-dalam dan lanjutkan dengan tenang."
    }
  }
  
  if (stressIndicators.speechRate > 200) {
    return "Tidak perlu terburu-buru. Saya akan mendengarkan dengan sabar."
  }
  
  if (stressIndicators.pauseFrequency > 15) {
    return "Tidak apa-apa jika perlu waktu untuk berpikir. Saya akan menunggu."
  }
  
  return null // No intervention needed
}

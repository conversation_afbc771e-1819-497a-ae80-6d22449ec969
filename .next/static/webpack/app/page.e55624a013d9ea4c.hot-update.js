"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   geminiTextToSpeech: () => (/* binding */ geminiTextToSpeech),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Function to process user input with Gemini AI\nasync function processUserInput(userInput) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        const prompt = 'Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\\n\\n    Respons kandidat: \"'.concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based)\nfunction speechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Voice options for Gemini TTS\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"female-1\",\n        name: \"Gemini Female\",\n        description: \"Suara perempuan yang natural dari Gemini AI\"\n    },\n    male: {\n        id: \"male-1\",\n        name: \"Gemini Male\",\n        description: \"Suara laki-laki yang natural dari Gemini AI\"\n    }\n};\n// Function to convert text to speech using Gemini TTS API with voice selection\nasync function geminiTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with Gemini API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!apiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Get the voice configuration based on gender selection\n        const voiceConfig = VOICE_OPTIONS[voiceGender];\n        console.log(\"Using voice: \".concat(voiceConfig.name));\n        console.log(\"Selected gender: \".concat(voiceGender));\n        // Prepare the request body for Gemini TTS\n        const requestBody = {\n            input: {\n                text: text\n            },\n            voice: {\n                languageCode: \"id-ID\",\n                name: voiceGender === 'female' ? \"id-ID-Standard-A\" : \"id-ID-Standard-B\",\n                ssmlGender: voiceGender === 'female' ? \"FEMALE\" : \"MALE\"\n            },\n            audioConfig: {\n                audioEncoding: \"MP3\",\n                speakingRate: 1.0,\n                pitch: 0.0,\n                volumeGainDb: 0.0\n            }\n        };\n        console.log(\"Sending text to Gemini TTS API...\");\n        console.log(\"Request body:\", JSON.stringify(requestBody, null, 2));\n        // Make the API request to Google Cloud Text-to-Speech (Gemini)\n        const response = await fetch(\"https://texttospeech.googleapis.com/v1/text:synthesize?key=\".concat(apiKey), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from Gemini TTS API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n            // If parsing JSON fails, use the status text\n            }\n            throw new Error(\"Gemini TTS API error: \".concat(errorMessage));\n        }\n        const data = await response.json();\n        // The response contains base64 encoded audio\n        if (!data.audioContent) {\n            throw new Error(\"No audio content received from Gemini TTS API\");\n        }\n        // Convert base64 to ArrayBuffer\n        const audioData = Uint8Array.from(atob(data.audioContent), (c)=>c.charCodeAt(0)).buffer;\n        console.log(\"Successfully generated speech audio with Gemini TTS\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with Gemini TTS:\", error);\n        throw new Error(\"Failed to generate speech with Gemini TTS API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech with voice gender selection\nasync function textToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        // Use ElevenLabs API to generate speech (primary)\n        const audioData = await elevenLabsTextToSpeech(text, voiceGender);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with ElevenLabs TTS, using browser TTS fallback:\", error);\n        // Fall back to browser's built-in TTS if ElevenLabs fails\n        return new Promise((resolve, reject)=>{\n            if ( false || !(\"speechSynthesis\" in window)) {\n                reject(new Error(\"Text-to-speech not supported in this browser\"));\n                return;\n            }\n            // Create a speech utterance\n            const utterance = new SpeechSynthesisUtterance(text);\n            utterance.lang = \"id-ID\" // Indonesian language\n            ;\n            // Try to find an Indonesian voice based on gender preference\n            const voices = window.speechSynthesis.getVoices();\n            let selectedVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n            // If no Indonesian voice, try to find gender-appropriate voice\n            if (!selectedVoice) {\n                if (voiceGender === 'female') {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'));\n                } else {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'));\n                }\n            }\n            if (selectedVoice) {\n                utterance.voice = selectedVoice;\n            }\n            // Play the speech\n            window.speechSynthesis.speak(utterance);\n            resolve(\"\");\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
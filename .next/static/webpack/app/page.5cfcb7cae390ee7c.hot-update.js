"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/ai-service */ \"(app-pages-browser)/./lib/ai-service.ts\");\n/* harmony import */ var _lib_psychological_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/psychological-service */ \"(app-pages-browser)/./lib/psychological-service.ts\");\n/* harmony import */ var _components_psychological_assessment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/psychological-assessment */ \"(app-pages-browser)/./components/psychological-assessment.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction InterviewPage() {\n    _s();\n    // State untuk menentukan apakah sudah memilih gender atau belum\n    const [hasSelectedGender, setHasSelectedGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voiceGender, setVoiceGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('female');\n    // State untuk psychological assessment\n    const [showPsychAssessment, setShowPsychAssessment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [psychologicalProfile, setPsychologicalProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State untuk interview\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Refs untuk audio recording\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function untuk memilih gender dan mulai psychological assessment\n    const handleGenderSelection = (gender)=>{\n        setVoiceGender(gender);\n        setShowPsychAssessment(true);\n    };\n    // Function untuk menyelesaikan psychological assessment\n    const handlePsychAssessmentComplete = (profile)=>{\n        setPsychologicalProfile(profile);\n        setShowPsychAssessment(false);\n        setHasSelectedGender(true);\n    };\n    // Initialize interview setelah gender dipilih\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            if (hasSelectedGender && !isInitialized) {\n                const initializeInterview = {\n                    \"InterviewPage.useEffect.initializeInterview\": async ()=>{\n                        setIsProcessing(true);\n                        try {\n                            console.log(\"Initializing interview...\");\n                            // Generate adaptive prompts based on psychological profile\n                            let initialPrompt = \"Perkenalkan diri Anda sebagai AI interviewer dan mulai wawancara kerja dalam bahasa Indonesia.\";\n                            let welcomeMessage = \"\";\n                            if (psychologicalProfile) {\n                                const adaptivePrompts = (0,_lib_psychological_service__WEBPACK_IMPORTED_MODULE_6__.generateAdaptivePrompts)(psychologicalProfile);\n                                initialPrompt = adaptivePrompts.systemPrompt + \" \" + initialPrompt;\n                                welcomeMessage = adaptivePrompts.welcomeMessage;\n                            }\n                            console.log(\"Sending adaptive initial prompt to AI...\");\n                            const initialResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(welcomeMessage || initialPrompt, psychologicalProfile, []);\n                            console.log(\"Received initial AI response:\", initialResponse);\n                            // Convert the initial response to speech\n                            let audioUrl = \"\";\n                            try {\n                                console.log(\"Converting initial response to speech...\");\n                                audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(initialResponse, voiceGender);\n                            } catch (speechError) {\n                                console.warn(\"Text-to-speech failed, continuing without audio:\", speechError);\n                            }\n                            setMessages([\n                                {\n                                    role: \"assistant\",\n                                    content: initialResponse,\n                                    audioUrl: audioUrl\n                                }\n                            ]);\n                            console.log(\"Interview initialized successfully\");\n                        } catch (error) {\n                            console.error(\"Error initializing interview:\", error);\n                            if (error instanceof Error) {\n                                setError(\"Gagal memulai wawancara: \".concat(error.message, \". Silakan periksa konfigurasi API key.\"));\n                            } else {\n                                setError(\"Gagal memulai wawancara. Silakan muat ulang halaman.\");\n                            }\n                        } finally{\n                            setIsProcessing(false);\n                            setIsInitialized(true);\n                        }\n                    }\n                }[\"InterviewPage.useEffect.initializeInterview\"];\n                initializeInterview();\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        hasSelectedGender,\n        isInitialized,\n        voiceGender\n    ]);\n    // Start the recording timer\n    const startTimer = ()=>{\n        setRecordingTime(0);\n        timerRef.current = setInterval(()=>{\n            setRecordingTime((prevTime)=>prevTime + 1);\n        }, 1000);\n    };\n    // Stop the recording timer\n    const stopTimer = ()=>{\n        if (timerRef.current) {\n            clearInterval(timerRef.current);\n            timerRef.current = null;\n        }\n    };\n    // Start recording using MediaRecorder API\n    const startRecording = async ()=>{\n        setIsRecording(true);\n        audioChunksRef.current = [];\n        try {\n            // Request microphone permission\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            // Create a new MediaRecorder instance\n            const mediaRecorder = new MediaRecorder(stream);\n            mediaRecorderRef.current = mediaRecorder;\n            // Set up event handlers\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    audioChunksRef.current.push(event.data);\n                }\n            };\n            // Start recording\n            mediaRecorder.start();\n            startTimer();\n        } catch (error) {\n            console.error(\"Error accessing microphone:\", error);\n            setError(\"Gagal mengakses mikrofon. Pastikan Anda memberikan izin.\");\n            setIsRecording(false);\n        }\n    };\n    // Stop recording and process the audio\n    const stopRecording = async ()=>{\n        setIsRecording(false);\n        stopTimer();\n        if (!mediaRecorderRef.current) {\n            setError(\"Tidak ada rekaman yang sedang berlangsung.\");\n            return;\n        }\n        try {\n            // Create a Promise that resolves when the MediaRecorder stops\n            const recordingData = await new Promise((resolve)=>{\n                mediaRecorderRef.current.onstop = ()=>{\n                    const audioBlob = new Blob(audioChunksRef.current, {\n                        type: 'audio/webm'\n                    });\n                    resolve(audioBlob);\n                };\n                mediaRecorderRef.current.stop();\n            });\n            // Stop all tracks in the stream\n            mediaRecorderRef.current.stream.getTracks().forEach((track)=>track.stop());\n            // Process the recording\n            await processRecording(recordingData);\n        } catch (error) {\n            console.error(\"Error stopping recording:\", error);\n            setError(\"Terjadi kesalahan saat menghentikan rekaman. Silakan coba lagi.\");\n            setIsProcessing(false);\n        }\n    };\n    // Process audio recording\n    const processRecording = async (audioData)=>{\n        setIsProcessing(true);\n        try {\n            console.log(\"Starting to process audio recording...\");\n            // Convert speech to text using Groq Whisper API\n            console.log(\"Converting speech to text with Groq Whisper...\");\n            const transcript = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.speechToText)(audioData);\n            console.log(\"Received transcript:\", transcript);\n            if (!transcript || transcript.trim() === \"\") {\n                throw new Error(\"Tidak ada teks yang terdeteksi dalam rekaman. Silakan coba lagi dengan suara yang lebih jelas.\");\n            }\n            // Add user message\n            const userMessage = {\n                role: \"user\",\n                content: transcript\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Process with AI and get response\n            console.log(\"Sending transcript to AI for processing...\");\n            const aiResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(transcript);\n            console.log(\"Received AI response:\", aiResponse);\n            // Convert AI response to speech\n            console.log(\"Converting AI response to speech...\");\n            const audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(aiResponse, voiceGender);\n            // Add AI message\n            const assistantMessage = {\n                role: \"assistant\",\n                content: aiResponse,\n                audioUrl: audioUrl\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            console.log(\"Processing complete\");\n        } catch (error) {\n            console.error(\"Error processing recording:\", error);\n            if (error instanceof Error) {\n                setError(\"Terjadi kesalahan: \".concat(error.message));\n            } else {\n                setError(\"Terjadi kesalahan saat memproses rekaman. Silakan coba lagi.\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    // Toggle recording state\n    const toggleRecording = ()=>{\n        if (isRecording) {\n            stopRecording();\n        } else {\n            startRecording();\n        }\n    };\n    // Function to play message text using text-to-speech\n    const playMessage = async (text, audioUrl)=>{\n        try {\n            if (audioUrl) {\n                // If we have a stored audio URL, play it directly\n                const audio = new Audio(audioUrl);\n                audio.play();\n            } else {\n                // Otherwise, generate new speech with current voice gender\n                await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(text, voiceGender);\n            }\n        } catch (error) {\n            console.error(\"Error playing message:\", error);\n            setError(\"Gagal memutar pesan. Silakan coba lagi.\");\n        }\n    };\n    // Function to export the transcript\n    const exportTranscript = ()=>{\n        try {\n            // Format the transcript\n            const date = new Date().toLocaleString(\"id-ID\");\n            let transcriptContent = \"AI Interview Transcript - \".concat(date, \"\\n\\n\");\n            messages.forEach((message)=>{\n                const role = message.role === \"assistant\" ? \"AI Interviewer\" : \"Kandidat\";\n                transcriptContent += \"\".concat(role, \": \").concat(message.content, \"\\n\\n\");\n            });\n            // Create a blob and download link\n            const blob = new Blob([\n                transcriptContent\n            ], {\n                type: \"text/plain;charset=utf-8\"\n            });\n            const url = URL.createObjectURL(blob);\n            // Create a temporary link and trigger download\n            const link = document.createElement(\"a\");\n            link.href = url;\n            link.download = \"interview-transcript-\".concat(new Date().toISOString().slice(0, 10), \".txt\");\n            document.body.appendChild(link);\n            link.click();\n            // Clean up\n            document.body.removeChild(link);\n            URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error(\"Error exporting transcript:\", error);\n            setError(\"Gagal mengekspor transkrip. Silakan coba lagi.\");\n        }\n    };\n    // Jika sedang menampilkan psychological assessment\n    if (showPsychAssessment) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_psychological_assessment__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            onComplete: handlePsychAssessmentComplete\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n            lineNumber: 302,\n            columnNumber: 12\n        }, this);\n    }\n    // Jika belum memilih gender, tampilkan halaman pilihan\n    if (!hasSelectedGender) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-10 w-10 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"AI Interview System\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-2\",\n                                children: \"Sistem Wawancara Kerja dengan Kecerdasan Buatan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Pilih suara AI interviewer yang membuat Anda nyaman\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm\",\n                                onClick: ()=>handleGenderSelection('female'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8 text-center h-full flex flex-col justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-pink-100 to-rose-100 rounded-full flex items-center justify-center group-hover:from-pink-200 group-hover:to-rose-200 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: \"\\uD83D\\uDC69‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Suara Perempuan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                                    children: \"Suara perempuan Indonesia yang natural dan profesional dari Google TTS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-sm text-gray-500 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Google TTS Indonesia\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pilih Suara Perempuan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm\",\n                                onClick: ()=>handleGenderSelection('male'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8 text-center h-full flex flex-col justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Suara Laki-laki\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                                    children: \"Suara laki-laki Indonesia yang natural dan profesional dari Google TTS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-sm text-gray-500 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Google TTS Indonesia\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pilih Suara Laki-laki\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Powered by Google TTS • Groq Whisper • Gemini AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n            lineNumber: 308,\n            columnNumber: 7\n        }, this);\n    }\n    // Interface wawancara dengan layout kiri-kanan\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"AI Interview System\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100\",\n                                    children: [\n                                        \"Suara: \",\n                                        _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: exportTranscript,\n                                    disabled: messages.length === 0,\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setHasSelectedGender(false),\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: \"Ganti Suara\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto p-4 h-[calc(100vh-80px)] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"h-80 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"bg-gray-50 py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Percakapan Interview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 h-72 overflow-y-auto flex flex-col-reverse\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"mt-2\",\n                                                onClick: ()=>setError(null),\n                                                children: \"Tutup\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 flex flex-col-reverse\",\n                                        children: [\n                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-6 w-6 animate-spin text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-600\",\n                                                        children: \"AI sedang memproses...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            messages.slice().reverse().map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.role === \"assistant\" ? \"justify-start\" : \"justify-end\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3 max-w-[85%] \".concat(message.role === \"assistant\" ? \"flex-row\" : \"flex-row-reverse\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10 \".concat(message.role === \"assistant\" ? \"bg-blue-100\" : \"bg-green-100\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg p-4 text-sm shadow-sm \".concat(message.role === \"assistant\" ? \"bg-blue-50 border-l-4 border-blue-400\" : \"bg-green-50 border-l-4 border-green-400\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-medium mb-1 \".concat(message.role === \"assistant\" ? \"text-blue-600\" : \"text-green-600\"),\n                                                                        children: message.role === \"assistant\" ? \"AI Interviewer\" : \"Anda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-800 leading-relaxed\",\n                                                                        children: message.content\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"mt-2 h-7 px-3 text-xs hover:bg-blue-100\",\n                                                                        onClick: ()=>playMessage(message.content, message.audioUrl),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                                lineNumber: 469,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" Putar Ulang\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, messages.length - 1 - index, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            messages.length === 0 && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: isInitialized ? \"Mulai berbicara dengan menekan tombol mikrofon di bawah\" : \"Memulai wawancara...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Percakapan terbaru akan muncul di atas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-blue-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"AI Interviewer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-12 w-12 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-600\",\n                                                children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full mt-2 \".concat(isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-green-400\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-green-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"Kandidat\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-12 w-12 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: toggleRecording,\n                                                        disabled: isProcessing,\n                                                        size: \"lg\",\n                                                        className: \"rounded-full h-14 w-14 mb-2 shadow-lg transition-all duration-200 \".concat(isRecording ? \"bg-red-500 hover:bg-red-600 animate-pulse\" : \"bg-green-500 hover:bg-green-600 hover:scale-105\"),\n                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 34\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 67\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: isProcessing ? \"Memproses...\" : isRecording ? \"Merekam... (\".concat(recordingTime, \"s)\") : \"Klik untuk berbicara\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mt-2 mx-auto \".concat(isRecording ? \"bg-red-400 animate-pulse\" : isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n        lineNumber: 388,\n        columnNumber: 5\n    }, this);\n}\n_s(InterviewPage, \"t20kZt2uCDPm904FQ/Kuq5+o/8M=\");\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});
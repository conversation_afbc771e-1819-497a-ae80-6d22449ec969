"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   elevenLabsTextToSpeech: () => (/* binding */ elevenLabsTextToSpeech),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Function to process user input with Gemini AI\nasync function processUserInput(userInput) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        const prompt = 'Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\\n\\n    Respons kandidat: \"'.concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based)\nfunction speechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Voice options for ElevenLabs\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"21m00Tcm4TlvDq8ikWAM\",\n        name: \"Rachel (Perempuan)\",\n        description: \"Suara perempuan yang natural dan profesional\"\n    },\n    male: {\n        id: \"pNInz6obpgDQGcFmaJgB\",\n        name: \"Adam (Laki-laki)\",\n        description: \"Suara laki-laki yang dalam dan tegas\"\n    }\n};\n// Function to convert text to speech using ElevenLabs API with voice selection\nasync function elevenLabsTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with ElevenLabs API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"***************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No ElevenLabs API key found in environment variables\");\n            throw new Error(\"ElevenLabs API key not configured\");\n        }\n        // Get the voice ID based on gender selection\n        const voiceId = VOICE_OPTIONS[voiceGender].id;\n        console.log(\"Using voice: \".concat(VOICE_OPTIONS[voiceGender].name));\n        console.log(\"Voice ID: \".concat(voiceId));\n        console.log(\"Selected gender: \".concat(voiceGender));\n        // Prepare the request body\n        const requestBody = {\n            text: text,\n            model_id: \"eleven_multilingual_v2\",\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true\n            }\n        };\n        console.log(\"Sending text to ElevenLabs API...\");\n        console.log(\"Request body:\", JSON.stringify(requestBody, null, 2));\n        // Make the API request to ElevenLabs\n        const response = await fetch(\"https://api.elevenlabs.io/v1/text-to-speech/\".concat(voiceId), {\n            method: \"POST\",\n            headers: {\n                \"Accept\": \"audio/mpeg\",\n                \"Content-Type\": \"application/json\",\n                \"xi-api-key\": apiKey\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from ElevenLabs API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_detail;\n                const errorData = await response.json();\n                console.error(\"ElevenLabs API Error Data:\", errorData);\n                errorMessage = ((_errorData_detail = errorData.detail) === null || _errorData_detail === void 0 ? void 0 : _errorData_detail.message) || errorData.message || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse ElevenLabs API error response\", e);\n            }\n            throw new Error(\"ElevenLabs API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio with ElevenLabs\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with ElevenLabs:\", error);\n        throw new Error(\"Failed to generate speech with ElevenLabs API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech with voice gender selection\nasync function textToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        // Use ElevenLabs API to generate speech (primary)\n        const audioData = await elevenLabsTextToSpeech(text, voiceGender);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with ElevenLabs TTS, using browser TTS fallback:\", error);\n        // Fall back to browser's built-in TTS if ElevenLabs fails\n        return new Promise((resolve, reject)=>{\n            if ( false || !(\"speechSynthesis\" in window)) {\n                reject(new Error(\"Text-to-speech not supported in this browser\"));\n                return;\n            }\n            // Create a speech utterance\n            const utterance = new SpeechSynthesisUtterance(text);\n            utterance.lang = \"id-ID\" // Indonesian language\n            ;\n            // Try to find an Indonesian voice based on gender preference\n            const voices = window.speechSynthesis.getVoices();\n            let selectedVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n            // If no Indonesian voice, try to find gender-appropriate voice\n            if (!selectedVoice) {\n                if (voiceGender === 'female') {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'));\n                } else {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'));\n                }\n            }\n            if (selectedVoice) {\n                utterance.voice = selectedVoice;\n            }\n            // Play the speech\n            window.speechSynthesis.speak(utterance);\n            resolve(\"\");\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
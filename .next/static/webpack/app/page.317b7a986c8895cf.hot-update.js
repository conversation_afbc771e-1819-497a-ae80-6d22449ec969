"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   browserSpeechToText: () => (/* binding */ browserSpeechToText),\n/* harmony export */   elevenLabsTextToSpeech: () => (/* binding */ elevenLabsTextToSpeech),\n/* harmony export */   groqSpeechToText: () => (/* binding */ groqSpeechToText),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Function to process user input with Gemini AI\nasync function processUserInput(userInput) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        const prompt = 'Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\\n\\n    Respons kandidat: \"'.concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Groq Whisper API\nasync function groqSpeechToText(audioBlob) {\n    try {\n        console.log(\"Starting transcription with Groq Whisper API...\");\n        // Get the API key from environment variables\n        const apiKey = \"********************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No Groq API key found in environment variables\");\n            throw new Error(\"Groq API key not configured\");\n        }\n        // Create a FormData object to send the audio file\n        const formData = new FormData();\n        // Determine the file extension based on the audio blob type\n        let fileExtension = 'webm';\n        if (audioBlob.type) {\n            const mimeType = audioBlob.type.split('/')[1];\n            if (mimeType) {\n                fileExtension = mimeType;\n            }\n        }\n        formData.append(\"file\", audioBlob, \"recording.\".concat(fileExtension));\n        formData.append(\"model\", \"whisper-large-v3\");\n        formData.append(\"language\", \"id\") // Indonesian language code (ISO-639-1)\n        ;\n        console.log(\"Sending audio file (\".concat(fileExtension, \") to Groq Whisper API...\"));\n        // Make the API request to Groq\n        const response = await fetch(\"https://api.groq.com/openai/v1/audio/transcriptions\", {\n            method: \"POST\",\n            headers: {\n                Authorization: \"Bearer \".concat(apiKey)\n            },\n            body: formData\n        });\n        console.log(\"Received response from Groq Whisper API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"Groq Whisper API Error Data:\", errorData);\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse Groq Whisper API error response\", e);\n            }\n            throw new Error(\"Groq Whisper API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        const data = await response.json();\n        console.log(\"Successfully transcribed audio with Groq Whisper\");\n        return data.text;\n    } catch (error) {\n        console.error(\"Error transcribing with Groq Whisper:\", error);\n        throw new Error(\"Failed to transcribe audio with Groq Whisper API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based) - fallback\nfunction browserSpeechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Main speech to text function that uses Groq Whisper API\nasync function speechToText(audioBlob) {\n    try {\n        // If an audio blob is provided, use Groq Whisper API\n        if (audioBlob) {\n            return await groqSpeechToText(audioBlob);\n        }\n        // Otherwise, fall back to browser-based speech recognition\n        return await browserSpeechToText();\n    } catch (error) {\n        console.error(\"Speech to text error:\", error);\n        throw error;\n    }\n}\n// Voice options for ElevenLabs\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"21m00Tcm4TlvDq8ikWAM\",\n        name: \"Rachel (Perempuan)\",\n        description: \"Suara perempuan yang natural dan profesional\"\n    },\n    male: {\n        id: \"pNInz6obpgDQGcFmaJgB\",\n        name: \"Adam (Laki-laki)\",\n        description: \"Suara laki-laki yang dalam dan tegas\"\n    }\n};\n// Function to convert text to speech using ElevenLabs API with voice selection\nasync function elevenLabsTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with ElevenLabs API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"***************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No ElevenLabs API key found in environment variables\");\n            throw new Error(\"ElevenLabs API key not configured\");\n        }\n        // Get the voice ID based on gender selection\n        const voiceId = VOICE_OPTIONS[voiceGender].id;\n        console.log(\"Using voice: \".concat(VOICE_OPTIONS[voiceGender].name));\n        console.log(\"Voice ID: \".concat(voiceId));\n        console.log(\"Selected gender: \".concat(voiceGender));\n        // Prepare the request body\n        const requestBody = {\n            text: text,\n            model_id: \"eleven_multilingual_v2\",\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true\n            }\n        };\n        console.log(\"Sending text to ElevenLabs API...\");\n        console.log(\"Request body:\", JSON.stringify(requestBody, null, 2));\n        // Make the API request to ElevenLabs\n        const response = await fetch(\"https://api.elevenlabs.io/v1/text-to-speech/\".concat(voiceId), {\n            method: \"POST\",\n            headers: {\n                \"Accept\": \"audio/mpeg\",\n                \"Content-Type\": \"application/json\",\n                \"xi-api-key\": apiKey\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from ElevenLabs API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_detail;\n                const errorData = await response.json();\n                console.error(\"ElevenLabs API Error Data:\", errorData);\n                errorMessage = ((_errorData_detail = errorData.detail) === null || _errorData_detail === void 0 ? void 0 : _errorData_detail.message) || errorData.message || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse ElevenLabs API error response\", e);\n            }\n            throw new Error(\"ElevenLabs API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio with ElevenLabs\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with ElevenLabs:\", error);\n        throw new Error(\"Failed to generate speech with ElevenLabs API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech with voice gender selection\nasync function textToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        // Use ElevenLabs API to generate speech (primary)\n        const audioData = await elevenLabsTextToSpeech(text, voiceGender);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with ElevenLabs TTS, using browser TTS fallback:\", error);\n        // Fall back to browser's built-in TTS if ElevenLabs fails\n        return new Promise((resolve, reject)=>{\n            if ( false || !(\"speechSynthesis\" in window)) {\n                reject(new Error(\"Text-to-speech not supported in this browser\"));\n                return;\n            }\n            // Create a speech utterance\n            const utterance = new SpeechSynthesisUtterance(text);\n            utterance.lang = \"id-ID\" // Indonesian language\n            ;\n            // Try to find an Indonesian voice based on gender preference\n            const voices = window.speechSynthesis.getVoices();\n            let selectedVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n            // If no Indonesian voice, try to find gender-appropriate voice\n            if (!selectedVoice) {\n                if (voiceGender === 'female') {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'));\n                } else {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'));\n                }\n            }\n            if (selectedVoice) {\n                utterance.voice = selectedVoice;\n            }\n            // Play the speech\n            window.speechSynthesis.speak(utterance);\n            resolve(\"\");\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
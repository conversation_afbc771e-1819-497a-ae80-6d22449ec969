"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   elevenLabsTextToSpeech: () => (/* binding */ elevenLabsTextToSpeech),\n/* harmony export */   groqTextToSpeech: () => (/* binding */ groqTextToSpeech),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js\");\n\n// Function to process user input with Gemini AI\nasync function processUserInput(userInput) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        const prompt = 'Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\\n\\n    Respons kandidat: \"'.concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based)\nfunction speechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Voice options for ElevenLabs\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"21m00Tcm4TlvDq8ikWAM\",\n        name: \"Rachel (Perempuan)\",\n        description: \"Suara perempuan yang natural dan profesional\"\n    },\n    male: {\n        id: \"29vD33N1CtxCmqQRPOHJ\",\n        name: \"Drew (Laki-laki)\",\n        description: \"Suara laki-laki yang natural dan profesional\"\n    }\n};\n// Function to convert text to speech using ElevenLabs API with voice selection\nasync function elevenLabsTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with ElevenLabs API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"***************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No ElevenLabs API key found in environment variables\");\n            throw new Error(\"ElevenLabs API key not configured\");\n        }\n        // Get the voice ID based on gender selection\n        const voiceId = VOICE_OPTIONS[voiceGender].id;\n        console.log(\"Using voice: \".concat(VOICE_OPTIONS[voiceGender].name));\n        // Prepare the request body\n        const requestBody = {\n            text: text,\n            model_id: \"eleven_multilingual_v2\",\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true\n            }\n        };\n        console.log(\"Sending text to ElevenLabs API...\");\n        // Make the API request to ElevenLabs\n        const response = await fetch(\"https://api.elevenlabs.io/v1/text-to-speech/\".concat(voiceId), {\n            method: \"POST\",\n            headers: {\n                \"Accept\": \"audio/mpeg\",\n                \"Content-Type\": \"application/json\",\n                \"xi-api-key\": apiKey\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from ElevenLabs API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_detail;\n                const errorData = await response.json();\n                errorMessage = ((_errorData_detail = errorData.detail) === null || _errorData_detail === void 0 ? void 0 : _errorData_detail.message) || errorData.message || errorMessage;\n            } catch (e) {\n            // If parsing JSON fails, use the status text\n            }\n            throw new Error(\"ElevenLabs API error: \".concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio with ElevenLabs\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with ElevenLabs:\", error);\n        throw new Error(\"Failed to generate speech with ElevenLabs API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech using Groq's PlayAI TTS API (fallback)\nasync function groqTextToSpeech(text) {\n    try {\n        console.log(\"Starting text-to-speech with Groq PlayAI TTS API...\");\n        // Get the API key from environment variables\n        const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY || process.env.GROQ_API_KEY;\n        if (!apiKey) {\n            console.error(\"No Groq API key found in environment variables\");\n            throw new Error(\"API key not configured\");\n        }\n        // Prepare the request body\n        const requestBody = {\n            model: \"playai-tts\",\n            input: text,\n            voice: \"alloy\",\n            speed: 1.0,\n            response_format: \"mp3\" // Output format\n        };\n        console.log(\"Sending text to Groq TTS API...\");\n        // Make the API request to Groq\n        const response = await fetch(\"https://api.groq.com/openai/v1/audio/speech\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(apiKey),\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from Groq TTS API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n            // If parsing JSON fails, use the status text\n            }\n            throw new Error(\"Groq TTS API error: \".concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with Groq:\", error);\n        throw new Error(\"Failed to generate speech with Groq API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech with voice gender selection\nasync function textToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        // Use ElevenLabs API to generate speech (primary)\n        const audioData = await elevenLabsTextToSpeech(text, voiceGender);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with ElevenLabs TTS, trying fallback:\", error);\n        // Fallback to Groq TTS\n        try {\n            const audioData = await groqTextToSpeech(text);\n            const audioBlob = new Blob([\n                audioData\n            ], {\n                type: 'audio/mp3'\n            });\n            const audioUrl = URL.createObjectURL(audioBlob);\n            const audio = new Audio(audioUrl);\n            audio.play();\n            return audioUrl;\n        } catch (groqError) {\n            console.error(\"Error with Groq TTS, using browser TTS:\", groqError);\n            // Fall back to browser's built-in TTS if both APIs fail\n            return new Promise((resolve, reject)=>{\n                if ( false || !(\"speechSynthesis\" in window)) {\n                    reject(new Error(\"Text-to-speech not supported in this browser\"));\n                    return;\n                }\n                // Create a speech utterance\n                const utterance = new SpeechSynthesisUtterance(text);\n                utterance.lang = \"id-ID\" // Indonesian language\n                ;\n                // Try to find an Indonesian voice based on gender preference\n                const voices = window.speechSynthesis.getVoices();\n                let selectedVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n                // If no Indonesian voice, try to find gender-appropriate voice\n                if (!selectedVoice) {\n                    if (voiceGender === 'female') {\n                        selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'));\n                    } else {\n                        selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'));\n                    }\n                }\n                if (selectedVoice) {\n                    utterance.voice = selectedVoice;\n                }\n                // Play the speech\n                window.speechSynthesis.speak(utterance);\n                resolve(\"\");\n            });\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   browserSpeechToText: () => (/* binding */ browserSpeechToText),\n/* harmony export */   elevenLabsTextToSpeech: () => (/* binding */ elevenLabsTextToSpeech),\n/* harmony export */   googleTextToSpeech: () => (/* binding */ googleTextToSpeech),\n/* harmony export */   groqSpeechToText: () => (/* binding */ groqSpeechToText),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Function to process user input with Gemini AI and psychological adaptation\nasync function processUserInput(userInput, psychologicalProfile, messageHistory) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        const prompt = 'Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\\n\\n    Respons kandidat: \"'.concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Groq Whisper API\nasync function groqSpeechToText(audioBlob) {\n    try {\n        console.log(\"Starting transcription with Groq Whisper API...\");\n        // Get the API key from environment variables\n        const apiKey = \"********************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No Groq API key found in environment variables\");\n            throw new Error(\"Groq API key not configured\");\n        }\n        // Create a FormData object to send the audio file\n        const formData = new FormData();\n        // Determine the file extension based on the audio blob type\n        let fileExtension = 'webm';\n        if (audioBlob.type) {\n            const mimeType = audioBlob.type.split('/')[1];\n            if (mimeType) {\n                fileExtension = mimeType;\n            }\n        }\n        formData.append(\"file\", audioBlob, \"recording.\".concat(fileExtension));\n        formData.append(\"model\", \"whisper-large-v3\");\n        formData.append(\"language\", \"id\") // Indonesian language code (ISO-639-1)\n        ;\n        console.log(\"Sending audio file (\".concat(fileExtension, \") to Groq Whisper API...\"));\n        // Make the API request to Groq\n        const response = await fetch(\"https://api.groq.com/openai/v1/audio/transcriptions\", {\n            method: \"POST\",\n            headers: {\n                Authorization: \"Bearer \".concat(apiKey)\n            },\n            body: formData\n        });\n        console.log(\"Received response from Groq Whisper API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"Groq Whisper API Error Data:\", errorData);\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse Groq Whisper API error response\", e);\n            }\n            throw new Error(\"Groq Whisper API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        const data = await response.json();\n        console.log(\"Successfully transcribed audio with Groq Whisper\");\n        return data.text;\n    } catch (error) {\n        console.error(\"Error transcribing with Groq Whisper:\", error);\n        throw new Error(\"Failed to transcribe audio with Groq Whisper API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based) - fallback\nfunction browserSpeechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Main speech to text function that uses Groq Whisper API\nasync function speechToText(audioBlob) {\n    try {\n        // If an audio blob is provided, use Groq Whisper API\n        if (audioBlob) {\n            return await groqSpeechToText(audioBlob);\n        }\n        // Otherwise, fall back to browser-based speech recognition\n        return await browserSpeechToText();\n    } catch (error) {\n        console.error(\"Speech to text error:\", error);\n        throw error;\n    }\n}\n// Voice options for Google TTS\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"id-ID-Standard-A\",\n        name: \"Google Female (Perempuan)\",\n        description: \"Suara perempuan Indonesia dari Google TTS\"\n    },\n    male: {\n        id: \"id-ID-Standard-C\",\n        name: \"Google Male (Laki-laki)\",\n        description: \"Suara laki-laki Indonesia dari Google TTS\"\n    }\n};\n// Function to convert text to speech using Google TTS API with voice selection\nasync function googleTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with Google TTS API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"AIzaSyBSooCPxEUP0uyaYRSNzg9S2qfdzZj-s7E\" || 0;\n        if (!apiKey) {\n            console.error(\"No Google TTS API key found in environment variables\");\n            throw new Error(\"Google TTS API key not configured\");\n        }\n        // Get the voice configuration based on gender selection\n        const voiceConfig = VOICE_OPTIONS[voiceGender];\n        console.log(\"Using voice: \".concat(voiceConfig.name));\n        console.log(\"Voice ID: \".concat(voiceConfig.id));\n        console.log(\"Selected gender: \".concat(voiceGender));\n        // Prepare the request body for Google TTS\n        const requestBody = {\n            input: {\n                text: text\n            },\n            voice: {\n                languageCode: \"id-ID\",\n                name: voiceConfig.id,\n                ssmlGender: voiceGender === 'female' ? \"FEMALE\" : \"MALE\"\n            },\n            audioConfig: {\n                audioEncoding: \"MP3\",\n                speakingRate: 1.0,\n                pitch: 0.0,\n                volumeGainDb: 0.0\n            }\n        };\n        console.log(\"Sending text to Google TTS API...\");\n        console.log(\"Request body:\", JSON.stringify(requestBody, null, 2));\n        // Make the API request to Google Cloud Text-to-Speech\n        const response = await fetch(\"https://texttospeech.googleapis.com/v1/text:synthesize?key=\".concat(apiKey), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from Google TTS API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"Google TTS API Error Data:\", errorData);\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse Google TTS API error response\", e);\n            }\n            throw new Error(\"Google TTS API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        const data = await response.json();\n        // The response contains base64 encoded audio\n        if (!data.audioContent) {\n            throw new Error(\"No audio content received from Google TTS API\");\n        }\n        // Convert base64 to ArrayBuffer\n        const audioData = Uint8Array.from(atob(data.audioContent), (c)=>c.charCodeAt(0)).buffer;\n        console.log(\"Successfully generated speech audio with Google TTS\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with Google TTS:\", error);\n        throw new Error(\"Failed to generate speech with Google TTS API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech using ElevenLabs API (backup)\nasync function elevenLabsTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with ElevenLabs API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"***************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No ElevenLabs API key found in environment variables\");\n            throw new Error(\"ElevenLabs API key not configured\");\n        }\n        // ElevenLabs voice IDs (backup configuration)\n        const elevenLabsVoices = {\n            female: \"21m00Tcm4TlvDq8ikWAM\",\n            male: \"pNInz6obpgDQGcFmaJgB\" // Adam\n        };\n        const voiceId = elevenLabsVoices[voiceGender];\n        console.log(\"Using ElevenLabs voice ID: \".concat(voiceId));\n        // Prepare the request body\n        const requestBody = {\n            text: text,\n            model_id: \"eleven_multilingual_v2\",\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true\n            }\n        };\n        console.log(\"Sending text to ElevenLabs API...\");\n        // Make the API request to ElevenLabs\n        const response = await fetch(\"https://api.elevenlabs.io/v1/text-to-speech/\".concat(voiceId), {\n            method: \"POST\",\n            headers: {\n                \"Accept\": \"audio/mpeg\",\n                \"Content-Type\": \"application/json\",\n                \"xi-api-key\": apiKey\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from ElevenLabs API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_detail;\n                const errorData = await response.json();\n                console.error(\"ElevenLabs API Error Data:\", errorData);\n                errorMessage = ((_errorData_detail = errorData.detail) === null || _errorData_detail === void 0 ? void 0 : _errorData_detail.message) || errorData.message || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse ElevenLabs API error response\", e);\n            }\n            throw new Error(\"ElevenLabs API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio with ElevenLabs\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with ElevenLabs:\", error);\n        throw new Error(\"Failed to generate speech with ElevenLabs API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech with voice gender selection\nasync function textToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        // Use Google TTS API to generate speech (primary)\n        const audioData = await googleTextToSpeech(text, voiceGender);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with Google TTS, trying ElevenLabs fallback:\", error);\n        // Fallback to ElevenLabs TTS\n        try {\n            const audioData = await elevenLabsTextToSpeech(text, voiceGender);\n            const audioBlob = new Blob([\n                audioData\n            ], {\n                type: 'audio/mp3'\n            });\n            const audioUrl = URL.createObjectURL(audioBlob);\n            const audio = new Audio(audioUrl);\n            audio.play();\n            return audioUrl;\n        } catch (elevenLabsError) {\n            console.error(\"Error with ElevenLabs TTS, using browser TTS fallback:\", elevenLabsError);\n            // Fall back to browser's built-in TTS if both APIs fail\n            return new Promise((resolve, reject)=>{\n                if ( false || !(\"speechSynthesis\" in window)) {\n                    reject(new Error(\"Text-to-speech not supported in this browser\"));\n                    return;\n                }\n                // Create a speech utterance\n                const utterance = new SpeechSynthesisUtterance(text);\n                utterance.lang = \"id-ID\" // Indonesian language\n                ;\n                // Try to find an Indonesian voice based on gender preference\n                const voices = window.speechSynthesis.getVoices();\n                let selectedVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n                // If no Indonesian voice, try to find gender-appropriate voice\n                if (!selectedVoice) {\n                    if (voiceGender === 'female') {\n                        selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'));\n                    } else {\n                        selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'));\n                    }\n                }\n                if (selectedVoice) {\n                    utterance.voice = selectedVoice;\n                }\n                // Play the speech\n                window.speechSynthesis.speak(utterance);\n                resolve(\"\");\n            });\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
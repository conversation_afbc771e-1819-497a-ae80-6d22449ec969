"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   browserSpeechToText: () => (/* binding */ browserSpeechToText),\n/* harmony export */   elevenLabsTextToSpeech: () => (/* binding */ elevenLabsTextToSpeech),\n/* harmony export */   groqSpeechToText: () => (/* binding */ groqSpeechToText),\n/* harmony export */   groqTextToSpeech: () => (/* binding */ groqTextToSpeech),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_groq__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/groq */ \"(app-pages-browser)/./node_modules/.pnpm/@ai-sdk+groq@1.2.9_zod@3.25.67/node_modules/@ai-sdk/groq/dist/index.mjs\");\n/* harmony import */ var ai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ai */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.67/node_modules/ai/dist/index.mjs\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js\");\n\n\n\n// Function to process user input with Gemini AI\nasync function processUserInput(userInput) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        const prompt = 'Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\\n\\n    Respons kandidat: \"'.concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Fallback to Groq if Gemini fails\n        return await processUserInputWithGroq(userInput);\n    }\n}\n// Fallback function using Groq\nasync function processUserInputWithGroq(userInput) {\n    try {\n        console.log(\"Falling back to Groq API...\");\n        // Get the API key from environment variables\n        const apiKey = \"********************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No Groq API key found in environment variables\");\n            throw new Error(\"API key not configured\");\n        }\n        // Set the API key in the environment for the Groq client to use\n        if (typeof process !== 'undefined') {\n            process.env.GROQ_API_KEY = apiKey;\n        }\n        // Create the Groq client (it will automatically use the GROQ_API_KEY from env)\n        const groqModel = (0,_ai_sdk_groq__WEBPACK_IMPORTED_MODULE_1__.groq)(\"llama-3.1-8b-instant\");\n        console.log(\"Sending request to Groq API...\");\n        const { text } = await (0,ai__WEBPACK_IMPORTED_MODULE_2__.generateText)({\n            model: groqModel,\n            prompt: \"You are an AI interviewer conducting a job interview in Indonesian language.\\n      You should ask relevant follow-up questions based on the candidate's responses.\\n      Keep your responses concise, professional, and engaging.\\n\\n      Candidate's response: \\\"\".concat(userInput, '\"\\n\\n      Your response as the interviewer:')\n        });\n        console.log(\"Received response from Groq API\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Groq:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based)\nfunction browserSpeechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Function to convert speech to text using Groq's Whisper API\nasync function groqSpeechToText(audioBlob) {\n    try {\n        console.log(\"Starting transcription with Groq Whisper API...\");\n        // Get the API key from environment variables\n        const apiKey = \"********************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No Groq API key found in environment variables\");\n            throw new Error(\"API key not configured\");\n        }\n        // Create a FormData object to send the audio file\n        const formData = new FormData();\n        // Determine the file extension based on the audio blob type\n        let fileExtension = 'webm';\n        if (audioBlob.type) {\n            const mimeType = audioBlob.type.split('/')[1];\n            if (mimeType) {\n                fileExtension = mimeType;\n            }\n        }\n        formData.append(\"file\", audioBlob, \"recording.\".concat(fileExtension));\n        formData.append(\"model\", \"whisper-large-v3\");\n        formData.append(\"language\", \"id\") // Indonesian language code (ISO-639-1)\n        ;\n        console.log(\"Sending audio file (\".concat(fileExtension, \") to Groq API...\"));\n        // Make the API request to Groq\n        const response = await fetch(\"https://api.groq.com/openai/v1/audio/transcriptions\", {\n            method: \"POST\",\n            headers: {\n                Authorization: \"Bearer \".concat(apiKey)\n            },\n            body: formData\n        });\n        console.log(\"Received response from Groq API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n            // If parsing JSON fails, use the status text\n            }\n            throw new Error(\"Groq API error: \".concat(errorMessage));\n        }\n        const data = await response.json();\n        console.log(\"Successfully transcribed audio\");\n        return data.text;\n    } catch (error) {\n        console.error(\"Error transcribing with Groq:\", error);\n        throw new Error(\"Failed to transcribe audio with Groq API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Main speech to text function that uses Groq's Whisper API\nasync function speechToText(audioBlob) {\n    try {\n        // If an audio blob is provided, use Groq's API\n        if (audioBlob) {\n            return await groqSpeechToText(audioBlob);\n        }\n        // Otherwise, fall back to browser-based speech recognition\n        return await browserSpeechToText();\n    } catch (error) {\n        console.error(\"Speech to text error:\", error);\n        throw error;\n    }\n}\n// Voice options for ElevenLabs\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"21m00Tcm4TlvDq8ikWAM\",\n        name: \"Rachel (Perempuan)\",\n        description: \"Suara perempuan yang natural dan profesional\"\n    },\n    male: {\n        id: \"29vD33N1CtxCmqQRPOHJ\",\n        name: \"Drew (Laki-laki)\",\n        description: \"Suara laki-laki yang natural dan profesional\"\n    }\n};\n// Function to convert text to speech using ElevenLabs API with voice selection\nasync function elevenLabsTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with ElevenLabs API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"***************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No ElevenLabs API key found in environment variables\");\n            throw new Error(\"ElevenLabs API key not configured\");\n        }\n        // Get the voice ID based on gender selection\n        const voiceId = VOICE_OPTIONS[voiceGender].id;\n        console.log(\"Using voice: \".concat(VOICE_OPTIONS[voiceGender].name));\n        // Prepare the request body\n        const requestBody = {\n            text: text,\n            model_id: \"eleven_multilingual_v2\",\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true\n            }\n        };\n        console.log(\"Sending text to ElevenLabs API...\");\n        // Make the API request to ElevenLabs\n        const response = await fetch(\"https://api.elevenlabs.io/v1/text-to-speech/\".concat(voiceId), {\n            method: \"POST\",\n            headers: {\n                \"Accept\": \"audio/mpeg\",\n                \"Content-Type\": \"application/json\",\n                \"xi-api-key\": apiKey\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from ElevenLabs API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_detail;\n                const errorData = await response.json();\n                errorMessage = ((_errorData_detail = errorData.detail) === null || _errorData_detail === void 0 ? void 0 : _errorData_detail.message) || errorData.message || errorMessage;\n            } catch (e) {\n            // If parsing JSON fails, use the status text\n            }\n            throw new Error(\"ElevenLabs API error: \".concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio with ElevenLabs\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with ElevenLabs:\", error);\n        throw new Error(\"Failed to generate speech with ElevenLabs API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech using Groq's PlayAI TTS API (fallback)\nasync function groqTextToSpeech(text) {\n    try {\n        console.log(\"Starting text-to-speech with Groq PlayAI TTS API...\");\n        // Get the API key from environment variables\n        const apiKey = \"********************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No Groq API key found in environment variables\");\n            throw new Error(\"API key not configured\");\n        }\n        // Prepare the request body\n        const requestBody = {\n            model: \"playai-tts\",\n            input: text,\n            voice: \"alloy\",\n            speed: 1.0,\n            response_format: \"mp3\" // Output format\n        };\n        console.log(\"Sending text to Groq TTS API...\");\n        // Make the API request to Groq\n        const response = await fetch(\"https://api.groq.com/openai/v1/audio/speech\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(apiKey),\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from Groq TTS API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n            // If parsing JSON fails, use the status text\n            }\n            throw new Error(\"Groq TTS API error: \".concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with Groq:\", error);\n        throw new Error(\"Failed to generate speech with Groq API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech\nasync function textToSpeech(text) {\n    try {\n        // Use ElevenLabs API to generate speech (primary)\n        const audioData = await elevenLabsTextToSpeech(text);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with ElevenLabs TTS, trying fallback:\", error);\n        // Fallback to Groq TTS\n        try {\n            const audioData = await groqTextToSpeech(text);\n            const audioBlob = new Blob([\n                audioData\n            ], {\n                type: 'audio/mp3'\n            });\n            const audioUrl = URL.createObjectURL(audioBlob);\n            const audio = new Audio(audioUrl);\n            audio.play();\n            return audioUrl;\n        } catch (groqError) {\n            console.error(\"Error with Groq TTS, using browser TTS:\", groqError);\n            // Fall back to browser's built-in TTS if both APIs fail\n            return new Promise((resolve, reject)=>{\n                if ( false || !(\"speechSynthesis\" in window)) {\n                    reject(new Error(\"Text-to-speech not supported in this browser\"));\n                    return;\n                }\n                // Create a speech utterance\n                const utterance = new SpeechSynthesisUtterance(text);\n                utterance.lang = \"id-ID\" // Indonesian language\n                ;\n                // Try to find an Indonesian voice\n                const voices = window.speechSynthesis.getVoices();\n                const indonesianVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n                if (indonesianVoice) {\n                    utterance.voice = indonesianVoice;\n                }\n                // Play the speech\n                window.speechSynthesis.speak(utterance);\n                resolve(\"\");\n            });\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
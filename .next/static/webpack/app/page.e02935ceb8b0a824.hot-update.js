"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/ai-service */ \"(app-pages-browser)/./lib/ai-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction InterviewPage() {\n    _s();\n    // State untuk menentukan apakah sudah memilih gender atau belum\n    const [hasSelectedGender, setHasSelectedGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voiceGender, setVoiceGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('female');\n    // State untuk interview\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Refs untuk audio recording\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function untuk memilih gender dan mulai interview\n    const handleGenderSelection = (gender)=>{\n        setVoiceGender(gender);\n        setHasSelectedGender(true);\n    };\n    // Initialize interview setelah gender dipilih\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            if (hasSelectedGender && !isInitialized) {\n                const initializeInterview = {\n                    \"InterviewPage.useEffect.initializeInterview\": async ()=>{\n                        setIsProcessing(true);\n                        try {\n                            console.log(\"Initializing interview...\");\n                            const initialPrompt = \"Perkenalkan diri Anda sebagai AI interviewer dan mulai wawancara kerja dalam bahasa Indonesia.\";\n                            console.log(\"Sending initial prompt to AI...\");\n                            const initialResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(initialPrompt);\n                            console.log(\"Received initial AI response:\", initialResponse);\n                            // Convert the initial response to speech\n                            let audioUrl = \"\";\n                            try {\n                                console.log(\"Converting initial response to speech...\");\n                                audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(initialResponse, voiceGender);\n                            } catch (speechError) {\n                                console.warn(\"Text-to-speech failed, continuing without audio:\", speechError);\n                            }\n                            setMessages([\n                                {\n                                    role: \"assistant\",\n                                    content: initialResponse,\n                                    audioUrl: audioUrl\n                                }\n                            ]);\n                            console.log(\"Interview initialized successfully\");\n                        } catch (error) {\n                            console.error(\"Error initializing interview:\", error);\n                            if (error instanceof Error) {\n                                setError(\"Gagal memulai wawancara: \".concat(error.message, \". Silakan periksa konfigurasi API key.\"));\n                            } else {\n                                setError(\"Gagal memulai wawancara. Silakan muat ulang halaman.\");\n                            }\n                        } finally{\n                            setIsProcessing(false);\n                            setIsInitialized(true);\n                        }\n                    }\n                }[\"InterviewPage.useEffect.initializeInterview\"];\n                initializeInterview();\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        hasSelectedGender,\n        isInitialized,\n        voiceGender\n    ]);\n    // Start the recording timer\n    const startTimer = ()=>{\n        setRecordingTime(0);\n        timerRef.current = setInterval(()=>{\n            setRecordingTime((prevTime)=>prevTime + 1);\n        }, 1000);\n    };\n    // Stop the recording timer\n    const stopTimer = ()=>{\n        if (timerRef.current) {\n            clearInterval(timerRef.current);\n            timerRef.current = null;\n        }\n    };\n    // Start recording using MediaRecorder API\n    const startRecording = async ()=>{\n        setIsRecording(true);\n        audioChunksRef.current = [];\n        try {\n            // Request microphone permission\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            // Create a new MediaRecorder instance\n            const mediaRecorder = new MediaRecorder(stream);\n            mediaRecorderRef.current = mediaRecorder;\n            // Set up event handlers\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    audioChunksRef.current.push(event.data);\n                }\n            };\n            // Start recording\n            mediaRecorder.start();\n            startTimer();\n        } catch (error) {\n            console.error(\"Error accessing microphone:\", error);\n            setError(\"Gagal mengakses mikrofon. Pastikan Anda memberikan izin.\");\n            setIsRecording(false);\n        }\n    };\n    // Stop recording and process the audio\n    const stopRecording = async ()=>{\n        setIsRecording(false);\n        stopTimer();\n        if (!mediaRecorderRef.current) {\n            setError(\"Tidak ada rekaman yang sedang berlangsung.\");\n            return;\n        }\n        try {\n            // Create a Promise that resolves when the MediaRecorder stops\n            const recordingData = await new Promise((resolve)=>{\n                mediaRecorderRef.current.onstop = ()=>{\n                    const audioBlob = new Blob(audioChunksRef.current, {\n                        type: 'audio/webm'\n                    });\n                    resolve(audioBlob);\n                };\n                mediaRecorderRef.current.stop();\n            });\n            // Stop all tracks in the stream\n            mediaRecorderRef.current.stream.getTracks().forEach((track)=>track.stop());\n            // Process the recording\n            await processRecording(recordingData);\n        } catch (error) {\n            console.error(\"Error stopping recording:\", error);\n            setError(\"Terjadi kesalahan saat menghentikan rekaman. Silakan coba lagi.\");\n            setIsProcessing(false);\n        }\n    };\n    // Process audio recording\n    const processRecording = async (audioData)=>{\n        setIsProcessing(true);\n        try {\n            console.log(\"Starting to process audio recording...\");\n            // Convert speech to text using Groq Whisper API\n            console.log(\"Converting speech to text with Groq Whisper...\");\n            const transcript = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.speechToText)(audioData);\n            console.log(\"Received transcript:\", transcript);\n            if (!transcript || transcript.trim() === \"\") {\n                throw new Error(\"Tidak ada teks yang terdeteksi dalam rekaman. Silakan coba lagi dengan suara yang lebih jelas.\");\n            }\n            // Add user message\n            const userMessage = {\n                role: \"user\",\n                content: transcript\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Process with AI and get response\n            console.log(\"Sending transcript to AI for processing...\");\n            const aiResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(transcript);\n            console.log(\"Received AI response:\", aiResponse);\n            // Convert AI response to speech\n            console.log(\"Converting AI response to speech...\");\n            const audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(aiResponse, voiceGender);\n            // Add AI message\n            const assistantMessage = {\n                role: \"assistant\",\n                content: aiResponse,\n                audioUrl: audioUrl\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            console.log(\"Processing complete\");\n        } catch (error) {\n            console.error(\"Error processing recording:\", error);\n            if (error instanceof Error) {\n                setError(\"Terjadi kesalahan: \".concat(error.message));\n            } else {\n                setError(\"Terjadi kesalahan saat memproses rekaman. Silakan coba lagi.\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    // Toggle recording state\n    const toggleRecording = ()=>{\n        if (isRecording) {\n            stopRecording();\n        } else {\n            startRecording();\n        }\n    };\n    // Function to play message text using text-to-speech\n    const playMessage = async (text, audioUrl)=>{\n        try {\n            if (audioUrl) {\n                // If we have a stored audio URL, play it directly\n                const audio = new Audio(audioUrl);\n                audio.play();\n            } else {\n                // Otherwise, generate new speech with current voice gender\n                await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(text, voiceGender);\n            }\n        } catch (error) {\n            console.error(\"Error playing message:\", error);\n            setError(\"Gagal memutar pesan. Silakan coba lagi.\");\n        }\n    };\n    // Function to export the transcript\n    const exportTranscript = ()=>{\n        try {\n            // Format the transcript\n            const date = new Date().toLocaleString(\"id-ID\");\n            let transcriptContent = \"AI Interview Transcript - \".concat(date, \"\\n\\n\");\n            messages.forEach((message)=>{\n                const role = message.role === \"assistant\" ? \"AI Interviewer\" : \"Kandidat\";\n                transcriptContent += \"\".concat(role, \": \").concat(message.content, \"\\n\\n\");\n            });\n            // Create a blob and download link\n            const blob = new Blob([\n                transcriptContent\n            ], {\n                type: \"text/plain;charset=utf-8\"\n            });\n            const url = URL.createObjectURL(blob);\n            // Create a temporary link and trigger download\n            const link = document.createElement(\"a\");\n            link.href = url;\n            link.download = \"interview-transcript-\".concat(new Date().toISOString().slice(0, 10), \".txt\");\n            document.body.appendChild(link);\n            link.click();\n            // Clean up\n            document.body.removeChild(link);\n            URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error(\"Error exporting transcript:\", error);\n            setError(\"Gagal mengekspor transkrip. Silakan coba lagi.\");\n        }\n    };\n    // Jika belum memilih gender, tampilkan halaman pilihan\n    if (!hasSelectedGender) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"text-3xl font-bold\",\n                                children: \"AI Interview System\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-100 mt-2\",\n                                children: \"Pilih suara AI interviewer yang Anda inginkan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"Pilih Gender Suara AI Interviewer:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-8\",\n                                            children: \"Pilih suara yang membuat Anda merasa nyaman selama wawancara\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"cursor-pointer hover:shadow-lg transition-all duration-300 border-2 hover:border-pink-300\",\n                                            onClick: ()=>handleGenderSelection('female'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl mb-4\",\n                                                        children: \"\\uD83D\\uDC69‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-2\",\n                                                        children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS.female.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS.female.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"mt-4 w-full bg-pink-500 hover:bg-pink-600\",\n                                                        children: \"Pilih Suara Perempuan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"cursor-pointer hover:shadow-lg transition-all duration-300 border-2 hover:border-blue-300\",\n                                            onClick: ()=>handleGenderSelection('male'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl mb-4\",\n                                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-2\",\n                                                        children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS.male.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS.male.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"mt-4 w-full bg-blue-500 hover:bg-blue-600\",\n                                                        children: \"Pilih Suara Laki-laki\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n            lineNumber: 282,\n            columnNumber: 7\n        }, this);\n    }\n    // Interface wawancara dengan layout kiri-kanan\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"AI Interview System\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100\",\n                                    children: [\n                                        \"Suara: \",\n                                        _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: exportTranscript,\n                                    disabled: messages.length === 0,\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setHasSelectedGender(false),\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: \"Ganti Suara\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto p-4 h-[calc(100vh-80px)] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"h-80 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"bg-gray-50 py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Percakapan Interview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 h-72 overflow-y-auto flex flex-col-reverse\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"mt-2\",\n                                                onClick: ()=>setError(null),\n                                                children: \"Tutup\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 flex flex-col-reverse\",\n                                        children: [\n                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-6 w-6 animate-spin text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-600\",\n                                                        children: \"AI sedang memproses...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            messages.slice().reverse().map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.role === \"assistant\" ? \"justify-start\" : \"justify-end\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3 max-w-[85%] \".concat(message.role === \"assistant\" ? \"flex-row\" : \"flex-row-reverse\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10 \".concat(message.role === \"assistant\" ? \"bg-blue-100\" : \"bg-green-100\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg p-4 text-sm shadow-sm \".concat(message.role === \"assistant\" ? \"bg-blue-50 border-l-4 border-blue-400\" : \"bg-green-50 border-l-4 border-green-400\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-medium mb-1 \".concat(message.role === \"assistant\" ? \"text-blue-600\" : \"text-green-600\"),\n                                                                        children: message.role === \"assistant\" ? \"AI Interviewer\" : \"Anda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-800 leading-relaxed\",\n                                                                        children: message.content\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"mt-2 h-7 px-3 text-xs hover:bg-blue-100\",\n                                                                        onClick: ()=>playMessage(message.content, message.audioUrl),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" Putar Ulang\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, messages.length - 1 - index, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            messages.length === 0 && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: isInitialized ? \"Mulai berbicara dengan menekan tombol mikrofon di bawah\" : \"Memulai wawancara...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Percakapan terbaru akan muncul di atas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-blue-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"AI Interviewer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-12 w-12 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-600\",\n                                                children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full mt-2 \".concat(isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-green-400\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-green-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"Kandidat\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-12 w-12 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: toggleRecording,\n                                                        disabled: isProcessing,\n                                                        size: \"lg\",\n                                                        className: \"rounded-full h-14 w-14 mb-2 shadow-lg transition-all duration-200 \".concat(isRecording ? \"bg-red-500 hover:bg-red-600 animate-pulse\" : \"bg-green-500 hover:bg-green-600 hover:scale-105\"),\n                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 34\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 67\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: isProcessing ? \"Memproses...\" : isRecording ? \"Merekam... (\".concat(recordingTime, \"s)\") : \"Klik untuk berbicara\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mt-2 mx-auto \".concat(isRecording ? \"bg-red-400 animate-pulse\" : isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n        lineNumber: 335,\n        columnNumber: 5\n    }, this);\n}\n_s(InterviewPage, \"i03+s35BVCQn2A6/Dy90pzrGK3w=\");\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});
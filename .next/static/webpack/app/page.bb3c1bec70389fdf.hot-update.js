"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/ai-service */ \"(app-pages-browser)/./lib/ai-service.ts\");\n/* harmony import */ var _lib_psychological_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/psychological-service */ \"(app-pages-browser)/./lib/psychological-service.ts\");\n/* harmony import */ var _components_psychological_assessment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/psychological-assessment */ \"(app-pages-browser)/./components/psychological-assessment.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction InterviewPage() {\n    _s();\n    // State untuk menentukan apakah sudah memilih gender atau belum\n    const [hasSelectedGender, setHasSelectedGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voiceGender, setVoiceGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('female');\n    // State untuk psychological assessment\n    const [showPsychAssessment, setShowPsychAssessment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [psychologicalProfile, setPsychologicalProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State untuk psychological safety\n    const [showPsychSafety, setShowPsychSafety] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stressLevel, setStressLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    // State untuk interview\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Refs untuk audio recording\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function untuk memilih gender dan mulai psychological assessment\n    const handleGenderSelection = (gender)=>{\n        setVoiceGender(gender);\n        setShowPsychAssessment(true);\n    };\n    // Function untuk menyelesaikan psychological assessment\n    const handlePsychAssessmentComplete = (profile)=>{\n        setPsychologicalProfile(profile);\n        setShowPsychAssessment(false);\n        setHasSelectedGender(true);\n    };\n    // Initialize interview setelah gender dipilih\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            if (hasSelectedGender && !isInitialized) {\n                const initializeInterview = {\n                    \"InterviewPage.useEffect.initializeInterview\": async ()=>{\n                        setIsProcessing(true);\n                        try {\n                            console.log(\"Initializing interview...\");\n                            // Generate adaptive prompts based on psychological profile\n                            let initialPrompt = \"Perkenalkan diri Anda sebagai AI interviewer dan mulai wawancara kerja dalam bahasa Indonesia.\";\n                            let welcomeMessage = \"\";\n                            if (psychologicalProfile) {\n                                const adaptivePrompts = (0,_lib_psychological_service__WEBPACK_IMPORTED_MODULE_6__.generateAdaptivePrompts)(psychologicalProfile);\n                                initialPrompt = adaptivePrompts.systemPrompt + \" \" + initialPrompt;\n                                welcomeMessage = adaptivePrompts.welcomeMessage;\n                            }\n                            console.log(\"Sending adaptive initial prompt to AI...\");\n                            const initialResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(welcomeMessage || initialPrompt, psychologicalProfile || undefined, []);\n                            console.log(\"Received initial AI response:\", initialResponse);\n                            // Convert the initial response to speech\n                            let audioUrl = \"\";\n                            try {\n                                console.log(\"Converting initial response to speech...\");\n                                audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(initialResponse, voiceGender);\n                            } catch (speechError) {\n                                console.warn(\"Text-to-speech failed, continuing without audio:\", speechError);\n                            }\n                            setMessages([\n                                {\n                                    role: \"assistant\",\n                                    content: initialResponse,\n                                    audioUrl: audioUrl\n                                }\n                            ]);\n                            console.log(\"Interview initialized successfully\");\n                        } catch (error) {\n                            console.error(\"Error initializing interview:\", error);\n                            if (error instanceof Error) {\n                                setError(\"Gagal memulai wawancara: \".concat(error.message, \". Silakan periksa konfigurasi API key.\"));\n                            } else {\n                                setError(\"Gagal memulai wawancara. Silakan muat ulang halaman.\");\n                            }\n                        } finally{\n                            setIsProcessing(false);\n                            setIsInitialized(true);\n                        }\n                    }\n                }[\"InterviewPage.useEffect.initializeInterview\"];\n                initializeInterview();\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        hasSelectedGender,\n        isInitialized,\n        voiceGender\n    ]);\n    // Start the recording timer\n    const startTimer = ()=>{\n        setRecordingTime(0);\n        timerRef.current = setInterval(()=>{\n            setRecordingTime((prevTime)=>prevTime + 1);\n        }, 1000);\n    };\n    // Stop the recording timer\n    const stopTimer = ()=>{\n        if (timerRef.current) {\n            clearInterval(timerRef.current);\n            timerRef.current = null;\n        }\n    };\n    // Start recording using MediaRecorder API\n    const startRecording = async ()=>{\n        setIsRecording(true);\n        audioChunksRef.current = [];\n        try {\n            // Request microphone permission\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            // Create a new MediaRecorder instance\n            const mediaRecorder = new MediaRecorder(stream);\n            mediaRecorderRef.current = mediaRecorder;\n            // Set up event handlers\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    audioChunksRef.current.push(event.data);\n                }\n            };\n            // Start recording\n            mediaRecorder.start();\n            startTimer();\n        } catch (error) {\n            console.error(\"Error accessing microphone:\", error);\n            setError(\"Gagal mengakses mikrofon. Pastikan Anda memberikan izin.\");\n            setIsRecording(false);\n        }\n    };\n    // Stop recording and process the audio\n    const stopRecording = async ()=>{\n        setIsRecording(false);\n        stopTimer();\n        if (!mediaRecorderRef.current) {\n            setError(\"Tidak ada rekaman yang sedang berlangsung.\");\n            return;\n        }\n        try {\n            // Create a Promise that resolves when the MediaRecorder stops\n            const recordingData = await new Promise((resolve)=>{\n                mediaRecorderRef.current.onstop = ()=>{\n                    const audioBlob = new Blob(audioChunksRef.current, {\n                        type: 'audio/webm'\n                    });\n                    resolve(audioBlob);\n                };\n                mediaRecorderRef.current.stop();\n            });\n            // Stop all tracks in the stream\n            mediaRecorderRef.current.stream.getTracks().forEach((track)=>track.stop());\n            // Process the recording\n            await processRecording(recordingData);\n        } catch (error) {\n            console.error(\"Error stopping recording:\", error);\n            setError(\"Terjadi kesalahan saat menghentikan rekaman. Silakan coba lagi.\");\n            setIsProcessing(false);\n        }\n    };\n    // Process audio recording\n    const processRecording = async (audioData)=>{\n        setIsProcessing(true);\n        try {\n            console.log(\"Starting to process audio recording...\");\n            // Convert speech to text using Groq Whisper API\n            console.log(\"Converting speech to text with Groq Whisper...\");\n            const transcript = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.speechToText)(audioData);\n            console.log(\"Received transcript:\", transcript);\n            if (!transcript || transcript.trim() === \"\") {\n                throw new Error(\"Tidak ada teks yang terdeteksi dalam rekaman. Silakan coba lagi dengan suara yang lebih jelas.\");\n            }\n            // Add user message\n            const userMessage = {\n                role: \"user\",\n                content: transcript\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Process with AI and get response with psychological adaptation\n            console.log(\"Sending transcript to AI for processing...\");\n            const messageHistoryForAI = messages.map((m)=>({\n                    role: m.role,\n                    content: m.content\n                }));\n            const aiResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(transcript, psychologicalProfile || undefined, messageHistoryForAI);\n            console.log(\"Received AI response:\", aiResponse);\n            // Convert AI response to speech\n            console.log(\"Converting AI response to speech...\");\n            const audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(aiResponse, voiceGender);\n            // Add AI message\n            const assistantMessage = {\n                role: \"assistant\",\n                content: aiResponse,\n                audioUrl: audioUrl\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            console.log(\"Processing complete\");\n        } catch (error) {\n            console.error(\"Error processing recording:\", error);\n            if (error instanceof Error) {\n                setError(\"Terjadi kesalahan: \".concat(error.message));\n            } else {\n                setError(\"Terjadi kesalahan saat memproses rekaman. Silakan coba lagi.\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    // Toggle recording state\n    const toggleRecording = ()=>{\n        if (isRecording) {\n            stopRecording();\n        } else {\n            startRecording();\n        }\n    };\n    // Function to play message text using text-to-speech\n    const playMessage = async (text, audioUrl)=>{\n        try {\n            if (audioUrl) {\n                // If we have a stored audio URL, play it directly\n                const audio = new Audio(audioUrl);\n                audio.play();\n            } else {\n                // Otherwise, generate new speech with current voice gender\n                await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(text, voiceGender);\n            }\n        } catch (error) {\n            console.error(\"Error playing message:\", error);\n            setError(\"Gagal memutar pesan. Silakan coba lagi.\");\n        }\n    };\n    // Function to export the transcript\n    const exportTranscript = ()=>{\n        try {\n            // Format the transcript\n            const date = new Date().toLocaleString(\"id-ID\");\n            let transcriptContent = \"AI Interview Transcript - \".concat(date, \"\\n\\n\");\n            messages.forEach((message)=>{\n                const role = message.role === \"assistant\" ? \"AI Interviewer\" : \"Kandidat\";\n                transcriptContent += \"\".concat(role, \": \").concat(message.content, \"\\n\\n\");\n            });\n            // Create a blob and download link\n            const blob = new Blob([\n                transcriptContent\n            ], {\n                type: \"text/plain;charset=utf-8\"\n            });\n            const url = URL.createObjectURL(blob);\n            // Create a temporary link and trigger download\n            const link = document.createElement(\"a\");\n            link.href = url;\n            link.download = \"interview-transcript-\".concat(new Date().toISOString().slice(0, 10), \".txt\");\n            document.body.appendChild(link);\n            link.click();\n            // Clean up\n            document.body.removeChild(link);\n            URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error(\"Error exporting transcript:\", error);\n            setError(\"Gagal mengekspor transkrip. Silakan coba lagi.\");\n        }\n    };\n    // Jika sedang menampilkan psychological assessment\n    if (showPsychAssessment) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_psychological_assessment__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            onComplete: handlePsychAssessmentComplete\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n            lineNumber: 308,\n            columnNumber: 12\n        }, this);\n    }\n    // Jika belum memilih gender, tampilkan halaman pilihan\n    if (!hasSelectedGender) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-10 w-10 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"AI Interview System\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-2\",\n                                children: \"Sistem Wawancara Kerja dengan Kecerdasan Buatan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Pilih suara AI interviewer yang membuat Anda nyaman\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm\",\n                                onClick: ()=>handleGenderSelection('female'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8 text-center h-full flex flex-col justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-pink-100 to-rose-100 rounded-full flex items-center justify-center group-hover:from-pink-200 group-hover:to-rose-200 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: \"\\uD83D\\uDC69‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Suara Perempuan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                                    children: \"Suara perempuan Indonesia yang natural dan profesional dari Google TTS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-sm text-gray-500 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Google TTS Indonesia\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pilih Suara Perempuan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm\",\n                                onClick: ()=>handleGenderSelection('male'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8 text-center h-full flex flex-col justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Suara Laki-laki\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                                    children: \"Suara laki-laki Indonesia yang natural dan profesional dari Google TTS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-sm text-gray-500 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Google TTS Indonesia\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pilih Suara Laki-laki\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Powered by Google TTS • Groq Whisper • Gemini AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this);\n    }\n    // Interface wawancara dengan layout kiri-kanan\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"AI Interview System\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100\",\n                                    children: [\n                                        \"Suara: \",\n                                        _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: exportTranscript,\n                                    disabled: messages.length === 0,\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setHasSelectedGender(false),\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: \"Ganti Suara\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto p-4 h-[calc(100vh-80px)] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"h-80 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"bg-gray-50 py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Percakapan Interview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 h-72 overflow-y-auto flex flex-col-reverse\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"mt-2\",\n                                                onClick: ()=>setError(null),\n                                                children: \"Tutup\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 flex flex-col-reverse\",\n                                        children: [\n                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-6 w-6 animate-spin text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-600\",\n                                                        children: \"AI sedang memproses...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            messages.slice().reverse().map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.role === \"assistant\" ? \"justify-start\" : \"justify-end\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3 max-w-[85%] \".concat(message.role === \"assistant\" ? \"flex-row\" : \"flex-row-reverse\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10 \".concat(message.role === \"assistant\" ? \"bg-blue-100\" : \"bg-green-100\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg p-4 text-sm shadow-sm \".concat(message.role === \"assistant\" ? \"bg-blue-50 border-l-4 border-blue-400\" : \"bg-green-50 border-l-4 border-green-400\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-medium mb-1 \".concat(message.role === \"assistant\" ? \"text-blue-600\" : \"text-green-600\"),\n                                                                        children: message.role === \"assistant\" ? \"AI Interviewer\" : \"Anda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-800 leading-relaxed\",\n                                                                        children: message.content\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"mt-2 h-7 px-3 text-xs hover:bg-blue-100\",\n                                                                        onClick: ()=>playMessage(message.content, message.audioUrl),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" Putar Ulang\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, messages.length - 1 - index, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            messages.length === 0 && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: isInitialized ? \"Mulai berbicara dengan menekan tombol mikrofon di bawah\" : \"Memulai wawancara...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Percakapan terbaru akan muncul di atas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-blue-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"AI Interviewer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-12 w-12 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-600\",\n                                                children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full mt-2 \".concat(isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-green-400\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-green-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"Kandidat\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-12 w-12 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: toggleRecording,\n                                                        disabled: isProcessing,\n                                                        size: \"lg\",\n                                                        className: \"rounded-full h-14 w-14 mb-2 shadow-lg transition-all duration-200 \".concat(isRecording ? \"bg-red-500 hover:bg-red-600 animate-pulse\" : \"bg-green-500 hover:bg-green-600 hover:scale-105\"),\n                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 34\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 67\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: isProcessing ? \"Memproses...\" : isRecording ? \"Merekam... (\".concat(recordingTime, \"s)\") : \"Klik untuk berbicara\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mt-2 mx-auto \".concat(isRecording ? \"bg-red-400 animate-pulse\" : isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, this);\n}\n_s(InterviewPage, \"i/xoenAARJsLuEZRl14rl5wKxwI=\");\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});
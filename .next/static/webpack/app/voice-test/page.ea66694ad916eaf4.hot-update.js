"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/voice-test/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   browserSpeechToText: () => (/* binding */ browserSpeechToText),\n/* harmony export */   elevenLabsTextToSpeech: () => (/* binding */ elevenLabsTextToSpeech),\n/* harmony export */   googleTextToSpeech: () => (/* binding */ googleTextToSpeech),\n/* harmony export */   groqSpeechToText: () => (/* binding */ groqSpeechToText),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Function to process user input with Gemini AI and psychological adaptation\nasync function processUserInput(userInput, psychologicalProfile, messageHistory) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        // Build adaptive prompt based on psychological profile\n        let basePrompt = \"Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\";\n        // Add psychological adaptations\n        if (psychologicalProfile) {\n            const { generateAdaptivePrompts } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_psychological-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./psychological-service */ \"(app-pages-browser)/./lib/psychological-service.ts\"));\n            const adaptivePrompts = generateAdaptivePrompts(psychologicalProfile);\n            basePrompt = adaptivePrompts.systemPrompt;\n            // Add encouragement if needed\n            if (psychologicalProfile.needsEncouragement && messageHistory && messageHistory.length > 2) {\n                const lastUserMessage = messageHistory[messageHistory.length - 1];\n                if (lastUserMessage && lastUserMessage.role === 'user') {\n                    // Analyze if user seems stressed (simple heuristic)\n                    const userText = lastUserMessage.content.toLowerCase();\n                    const stressIndicators = [\n                        'um',\n                        'eh',\n                        'tidak tahu',\n                        'mungkin',\n                        'sepertinya',\n                        'kayaknya',\n                        'gimana ya'\n                    ];\n                    const hasStressIndicators = stressIndicators.some((indicator)=>userText.includes(indicator));\n                    if (hasStressIndicators) {\n                        basePrompt += \" PENTING: Kandidat terlihat sedikit nervous. Berikan positive reinforcement dan dukungan dalam respons Anda.\";\n                    }\n                }\n            }\n        } else {\n            basePrompt += \"\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\";\n        }\n        const prompt = \"\".concat(basePrompt, '\\n\\n    Respons kandidat: \"').concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Groq Whisper API\nasync function groqSpeechToText(audioBlob) {\n    try {\n        console.log(\"Starting transcription with Groq Whisper API...\");\n        // Get the API key from environment variables\n        const apiKey = \"********************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No Groq API key found in environment variables\");\n            throw new Error(\"Groq API key not configured\");\n        }\n        // Create a FormData object to send the audio file\n        const formData = new FormData();\n        // Determine the file extension based on the audio blob type\n        let fileExtension = 'webm';\n        if (audioBlob.type) {\n            const mimeType = audioBlob.type.split('/')[1];\n            if (mimeType) {\n                fileExtension = mimeType;\n            }\n        }\n        formData.append(\"file\", audioBlob, \"recording.\".concat(fileExtension));\n        formData.append(\"model\", \"whisper-large-v3\");\n        formData.append(\"language\", \"id\") // Indonesian language code (ISO-639-1)\n        ;\n        console.log(\"Sending audio file (\".concat(fileExtension, \") to Groq Whisper API...\"));\n        // Make the API request to Groq\n        const response = await fetch(\"https://api.groq.com/openai/v1/audio/transcriptions\", {\n            method: \"POST\",\n            headers: {\n                Authorization: \"Bearer \".concat(apiKey)\n            },\n            body: formData\n        });\n        console.log(\"Received response from Groq Whisper API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"Groq Whisper API Error Data:\", errorData);\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse Groq Whisper API error response\", e);\n            }\n            throw new Error(\"Groq Whisper API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        const data = await response.json();\n        console.log(\"Successfully transcribed audio with Groq Whisper\");\n        return data.text;\n    } catch (error) {\n        console.error(\"Error transcribing with Groq Whisper:\", error);\n        throw new Error(\"Failed to transcribe audio with Groq Whisper API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based) - fallback\nfunction browserSpeechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Main speech to text function that uses Groq Whisper API\nasync function speechToText(audioBlob) {\n    try {\n        // If an audio blob is provided, use Groq Whisper API\n        if (audioBlob) {\n            return await groqSpeechToText(audioBlob);\n        }\n        // Otherwise, fall back to browser-based speech recognition\n        return await browserSpeechToText();\n    } catch (error) {\n        console.error(\"Speech to text error:\", error);\n        throw error;\n    }\n}\n// Voice options for Google TTS\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"id-ID-Standard-A\",\n        name: \"Google Female (Perempuan)\",\n        description: \"Suara perempuan Indonesia dari Google TTS\"\n    },\n    male: {\n        id: \"id-ID-Standard-C\",\n        name: \"Google Male (Laki-laki)\",\n        description: \"Suara laki-laki Indonesia dari Google TTS\"\n    }\n};\n// Function to convert text to speech using Google TTS API with voice selection\nasync function googleTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with Google TTS API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"AIzaSyBSooCPxEUP0uyaYRSNzg9S2qfdzZj-s7E\" || 0;\n        if (!apiKey) {\n            console.error(\"No Google TTS API key found in environment variables\");\n            throw new Error(\"Google TTS API key not configured\");\n        }\n        // Get the voice configuration based on gender selection\n        const voiceConfig = VOICE_OPTIONS[voiceGender];\n        console.log(\"Using voice: \".concat(voiceConfig.name));\n        console.log(\"Voice ID: \".concat(voiceConfig.id));\n        console.log(\"Selected gender: \".concat(voiceGender));\n        // Prepare the request body for Google TTS\n        const requestBody = {\n            input: {\n                text: text\n            },\n            voice: {\n                languageCode: \"id-ID\",\n                name: voiceConfig.id,\n                ssmlGender: voiceGender === 'female' ? \"FEMALE\" : \"MALE\"\n            },\n            audioConfig: {\n                audioEncoding: \"MP3\",\n                speakingRate: 1.0,\n                pitch: 0.0,\n                volumeGainDb: 0.0\n            }\n        };\n        console.log(\"Sending text to Google TTS API...\");\n        console.log(\"Request body:\", JSON.stringify(requestBody, null, 2));\n        // Make the API request to Google Cloud Text-to-Speech\n        const response = await fetch(\"https://texttospeech.googleapis.com/v1/text:synthesize?key=\".concat(apiKey), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from Google TTS API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                console.error(\"Google TTS API Error Data:\", errorData);\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse Google TTS API error response\", e);\n            }\n            throw new Error(\"Google TTS API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        const data = await response.json();\n        // The response contains base64 encoded audio\n        if (!data.audioContent) {\n            throw new Error(\"No audio content received from Google TTS API\");\n        }\n        // Convert base64 to ArrayBuffer\n        const audioData = Uint8Array.from(atob(data.audioContent), (c)=>c.charCodeAt(0)).buffer;\n        console.log(\"Successfully generated speech audio with Google TTS\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with Google TTS:\", error);\n        throw new Error(\"Failed to generate speech with Google TTS API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech using ElevenLabs API (backup)\nasync function elevenLabsTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with ElevenLabs API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"***************************************************\" || 0;\n        if (!apiKey) {\n            console.error(\"No ElevenLabs API key found in environment variables\");\n            throw new Error(\"ElevenLabs API key not configured\");\n        }\n        // ElevenLabs voice IDs (backup configuration)\n        const elevenLabsVoices = {\n            female: \"21m00Tcm4TlvDq8ikWAM\",\n            male: \"pNInz6obpgDQGcFmaJgB\" // Adam\n        };\n        const voiceId = elevenLabsVoices[voiceGender];\n        console.log(\"Using ElevenLabs voice ID: \".concat(voiceId));\n        // Prepare the request body\n        const requestBody = {\n            text: text,\n            model_id: \"eleven_multilingual_v2\",\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true\n            }\n        };\n        console.log(\"Sending text to ElevenLabs API...\");\n        // Make the API request to ElevenLabs\n        const response = await fetch(\"https://api.elevenlabs.io/v1/text-to-speech/\".concat(voiceId), {\n            method: \"POST\",\n            headers: {\n                \"Accept\": \"audio/mpeg\",\n                \"Content-Type\": \"application/json\",\n                \"xi-api-key\": apiKey\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from ElevenLabs API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_detail;\n                const errorData = await response.json();\n                console.error(\"ElevenLabs API Error Data:\", errorData);\n                errorMessage = ((_errorData_detail = errorData.detail) === null || _errorData_detail === void 0 ? void 0 : _errorData_detail.message) || errorData.message || errorMessage;\n            } catch (e) {\n                console.error(\"Failed to parse ElevenLabs API error response\", e);\n            }\n            throw new Error(\"ElevenLabs API error (\".concat(response.status, \"): \").concat(errorMessage));\n        }\n        // Get the audio data as ArrayBuffer\n        const audioData = await response.arrayBuffer();\n        console.log(\"Successfully generated speech audio with ElevenLabs\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with ElevenLabs:\", error);\n        throw new Error(\"Failed to generate speech with ElevenLabs API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech with voice gender selection\nasync function textToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        // Use Google TTS API to generate speech (primary)\n        const audioData = await googleTextToSpeech(text, voiceGender);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with Google TTS, trying ElevenLabs fallback:\", error);\n        // Fallback to ElevenLabs TTS\n        try {\n            const audioData = await elevenLabsTextToSpeech(text, voiceGender);\n            const audioBlob = new Blob([\n                audioData\n            ], {\n                type: 'audio/mp3'\n            });\n            const audioUrl = URL.createObjectURL(audioBlob);\n            const audio = new Audio(audioUrl);\n            audio.play();\n            return audioUrl;\n        } catch (elevenLabsError) {\n            console.error(\"Error with ElevenLabs TTS, using browser TTS fallback:\", elevenLabsError);\n            // Fall back to browser's built-in TTS if both APIs fail\n            return new Promise((resolve, reject)=>{\n                if ( false || !(\"speechSynthesis\" in window)) {\n                    reject(new Error(\"Text-to-speech not supported in this browser\"));\n                    return;\n                }\n                // Create a speech utterance\n                const utterance = new SpeechSynthesisUtterance(text);\n                utterance.lang = \"id-ID\" // Indonesian language\n                ;\n                // Try to find an Indonesian voice based on gender preference\n                const voices = window.speechSynthesis.getVoices();\n                let selectedVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n                // If no Indonesian voice, try to find gender-appropriate voice\n                if (!selectedVoice) {\n                    if (voiceGender === 'female') {\n                        selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'));\n                    } else {\n                        selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'));\n                    }\n                }\n                if (selectedVoice) {\n                    utterance.voice = selectedVoice;\n                }\n                // Play the speech\n                window.speechSynthesis.speak(utterance);\n                resolve(\"\");\n            });\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
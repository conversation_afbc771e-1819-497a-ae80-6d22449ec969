"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/voice-test/page",{

/***/ "(app-pages-browser)/./lib/ai-service.ts":
/*!***************************!*\
  !*** ./lib/ai-service.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VOICE_OPTIONS: () => (/* binding */ VOICE_OPTIONS),\n/* harmony export */   geminiTextToSpeech: () => (/* binding */ geminiTextToSpeech),\n/* harmony export */   processUserInput: () => (/* binding */ processUserInput),\n/* harmony export */   speechToText: () => (/* binding */ speechToText),\n/* harmony export */   textToSpeech: () => (/* binding */ textToSpeech)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/.pnpm/@google+generative-ai@0.24.1/node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Function to process user input with Gemini AI\nasync function processUserInput(userInput) {\n    try {\n        console.log(\"Processing user input with Gemini AI...\");\n        // Get the API key from environment variables\n        const geminiApiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!geminiApiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Initialize Gemini AI\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(geminiApiKey);\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        const prompt = 'Anda adalah seorang AI interviewer yang sedang melakukan wawancara kerja dalam bahasa Indonesia.\\n    Anda harus mengajukan pertanyaan lanjutan yang relevan berdasarkan respons kandidat.\\n    Jaga agar respons Anda singkat, profesional, dan menarik.\\n    Gunakan bahasa Indonesia yang baik dan benar.\\n\\n    Respons kandidat: \"'.concat(userInput, '\"\\n\\n    Respons Anda sebagai interviewer:');\n        console.log(\"Sending request to Gemini AI...\");\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        console.log(\"Received response from Gemini AI\");\n        return text;\n    } catch (error) {\n        console.error(\"Error processing with Gemini AI:\", error);\n        // Return a more specific error message if possible\n        if (error instanceof Error) {\n            return \"Maaf, terjadi kesalahan dalam memproses respons: \".concat(error.message, \". Bisakah Anda mengulangi jawaban Anda?\");\n        }\n        return \"Maaf, terjadi kesalahan dalam memproses respons. Bisakah Anda mengulangi jawaban Anda?\";\n    }\n}\n// Function to convert speech to text using Web Speech API (browser-based)\nfunction speechToText() {\n    return new Promise((resolve, reject)=>{\n        if ( false || !(\"webkitSpeechRecognition\" in window)) {\n            reject(new Error(\"Speech recognition not supported in this browser\"));\n            return;\n        }\n        // @ts-ignore - TypeScript doesn't know about webkitSpeechRecognition\n        const recognition = new window.webkitSpeechRecognition();\n        recognition.lang = \"id-ID\" // Indonesian language\n        ;\n        recognition.interimResults = false;\n        recognition.maxAlternatives = 1;\n        recognition.onresult = (event)=>{\n            const transcript = event.results[0][0].transcript;\n            resolve(transcript);\n        };\n        recognition.onerror = (event)=>{\n            reject(new Error(\"Speech recognition error: \".concat(event.error)));\n        };\n        recognition.start();\n    });\n}\n// Voice options for Gemini TTS\nconst VOICE_OPTIONS = {\n    female: {\n        id: \"female-1\",\n        name: \"Gemini Female\",\n        description: \"Suara perempuan yang natural dari Gemini AI\"\n    },\n    male: {\n        id: \"male-1\",\n        name: \"Gemini Male\",\n        description: \"Suara laki-laki yang natural dari Gemini AI\"\n    }\n};\n// Function to convert text to speech using Gemini TTS API with voice selection\nasync function geminiTextToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        console.log(\"Starting text-to-speech with Gemini API using \".concat(voiceGender, \" voice...\"));\n        // Get the API key from environment variables\n        const apiKey = \"AIzaSyB1UmKE0xLxmnDCGizfPZwr8sVZ9VF3yP8\" || 0;\n        if (!apiKey) {\n            console.error(\"No Gemini API key found in environment variables\");\n            throw new Error(\"Gemini API key not configured\");\n        }\n        // Get the voice configuration based on gender selection\n        const voiceConfig = VOICE_OPTIONS[voiceGender];\n        console.log(\"Using voice: \".concat(voiceConfig.name));\n        console.log(\"Selected gender: \".concat(voiceGender));\n        // Prepare the request body for Gemini TTS\n        const requestBody = {\n            input: {\n                text: text\n            },\n            voice: {\n                languageCode: \"id-ID\",\n                name: voiceGender === 'female' ? \"id-ID-Standard-A\" : \"id-ID-Standard-C\",\n                ssmlGender: voiceGender === 'female' ? \"FEMALE\" : \"MALE\"\n            },\n            audioConfig: {\n                audioEncoding: \"MP3\",\n                speakingRate: 1.0,\n                pitch: 0.0,\n                volumeGainDb: 0.0\n            }\n        };\n        console.log(\"Sending text to Gemini TTS API...\");\n        console.log(\"Request body:\", JSON.stringify(requestBody, null, 2));\n        // Make the API request to Google Cloud Text-to-Speech (Gemini)\n        const response = await fetch(\"https://texttospeech.googleapis.com/v1/text:synthesize?key=\".concat(apiKey), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log(\"Received response from Gemini TTS API with status: \".concat(response.status));\n        if (!response.ok) {\n            let errorMessage = response.statusText;\n            try {\n                var _errorData_error;\n                const errorData = await response.json();\n                errorMessage = ((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || errorMessage;\n            } catch (e) {\n            // If parsing JSON fails, use the status text\n            }\n            throw new Error(\"Gemini TTS API error: \".concat(errorMessage));\n        }\n        const data = await response.json();\n        // The response contains base64 encoded audio\n        if (!data.audioContent) {\n            throw new Error(\"No audio content received from Gemini TTS API\");\n        }\n        // Convert base64 to ArrayBuffer\n        const audioData = Uint8Array.from(atob(data.audioContent), (c)=>c.charCodeAt(0)).buffer;\n        console.log(\"Successfully generated speech audio with Gemini TTS\");\n        return audioData;\n    } catch (error) {\n        console.error(\"Error generating speech with Gemini TTS:\", error);\n        throw new Error(\"Failed to generate speech with Gemini TTS API: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n// Function to convert text to speech with voice gender selection\nasync function textToSpeech(text) {\n    let voiceGender = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'female';\n    try {\n        // Use Gemini TTS API to generate speech (primary)\n        const audioData = await geminiTextToSpeech(text, voiceGender);\n        // Convert the ArrayBuffer to a Blob\n        const audioBlob = new Blob([\n            audioData\n        ], {\n            type: 'audio/mp3'\n        });\n        // Create a URL for the audio blob\n        const audioUrl = URL.createObjectURL(audioBlob);\n        // Play the audio\n        const audio = new Audio(audioUrl);\n        audio.play();\n        // Return the URL so it can be stored if needed\n        return audioUrl;\n    } catch (error) {\n        console.error(\"Error with Gemini TTS, using browser TTS fallback:\", error);\n        // Fall back to browser's built-in TTS if ElevenLabs fails\n        return new Promise((resolve, reject)=>{\n            if ( false || !(\"speechSynthesis\" in window)) {\n                reject(new Error(\"Text-to-speech not supported in this browser\"));\n                return;\n            }\n            // Create a speech utterance\n            const utterance = new SpeechSynthesisUtterance(text);\n            utterance.lang = \"id-ID\" // Indonesian language\n            ;\n            // Try to find an Indonesian voice based on gender preference\n            const voices = window.speechSynthesis.getVoices();\n            let selectedVoice = voices.find((voice)=>voice.lang.includes(\"id-ID\"));\n            // If no Indonesian voice, try to find gender-appropriate voice\n            if (!selectedVoice) {\n                if (voiceGender === 'female') {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman'));\n                } else {\n                    selectedVoice = voices.find((voice)=>voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man'));\n                }\n            }\n            if (selectedVoice) {\n                utterance.voice = selectedVoice;\n            }\n            // Play the speech\n            window.speechSynthesis.speak(utterance);\n            resolve(\"\");\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ai-service.ts\n"));

/***/ })

});
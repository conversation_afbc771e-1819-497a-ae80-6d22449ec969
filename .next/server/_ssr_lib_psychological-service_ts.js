"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_psychological-service_ts";
exports.ids = ["_ssr_lib_psychological-service_ts"];
exports.modules = {

/***/ "(ssr)/./lib/psychological-service.ts":
/*!**************************************!*\
  !*** ./lib/psychological-service.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PSYCHOLOGICAL_ASSESSMENT_QUESTIONS: () => (/* binding */ PSYCHOLOGICAL_ASSESSMENT_QUESTIONS),\n/* harmony export */   analyzePsychologicalProfile: () => (/* binding */ analyzePsychologicalProfile),\n/* harmony export */   analyzeStressIndicators: () => (/* binding */ analyzeStressIndicators),\n/* harmony export */   generateAdaptivePrompts: () => (/* binding */ generateAdaptivePrompts),\n/* harmony export */   generateRealTimeEncouragement: () => (/* binding */ generateRealTimeEncouragement)\n/* harmony export */ });\n// Psychological Assessment and Comfort Features for AI Interview\n// Menangani aspek psikologis yang tidak kasat mata namun sangat penting\n// Pre-interview psychological assessment questions\nconst PSYCHOLOGICAL_ASSESSMENT_QUESTIONS = [\n    {\n        id: 'anxiety_level',\n        question: 'Bagaimana perasaan Anda saat ini menjelang wawancara?',\n        options: [\n            {\n                value: 'low',\n                label: 'Tenang dan siap',\n                weight: 1\n            },\n            {\n                value: 'moderate',\n                label: 'Sedikit nervous tapi optimis',\n                weight: 2\n            },\n            {\n                value: 'high',\n                label: 'Sangat nervous dan khawatir',\n                weight: 3\n            }\n        ]\n    },\n    {\n        id: 'communication_preference',\n        question: 'Gaya komunikasi seperti apa yang membuat Anda nyaman?',\n        options: [\n            {\n                value: 'direct',\n                label: 'Langsung to the point',\n                weight: 1\n            },\n            {\n                value: 'gentle',\n                label: 'Lembut dan bertahap',\n                weight: 2\n            },\n            {\n                value: 'encouraging',\n                label: 'Penuh semangat dan motivasi',\n                weight: 3\n            },\n            {\n                value: 'formal',\n                label: 'Formal dan profesional',\n                weight: 4\n            }\n        ]\n    },\n    {\n        id: 'personality_type',\n        question: 'Dalam situasi sosial, Anda lebih suka:',\n        options: [\n            {\n                value: 'introvert',\n                label: 'Mendengarkan dan berpikir dulu sebelum bicara',\n                weight: 1\n            },\n            {\n                value: 'extrovert',\n                label: 'Langsung berbicara dan berinteraksi aktif',\n                weight: 2\n            },\n            {\n                value: 'ambivert',\n                label: 'Tergantung situasi dan mood',\n                weight: 3\n            }\n        ]\n    },\n    {\n        id: 'interview_experience',\n        question: 'Apakah Anda pernah mengikuti wawancara kerja sebelumnya?',\n        options: [\n            {\n                value: 'experienced',\n                label: 'Ya, sudah sering',\n                weight: 1\n            },\n            {\n                value: 'some',\n                label: 'Beberapa kali',\n                weight: 2\n            },\n            {\n                value: 'first_time',\n                label: 'Ini pertama kali',\n                weight: 3\n            }\n        ]\n    },\n    {\n        id: 'language_confidence',\n        question: 'Seberapa percaya diri Anda berbicara dalam wawancara?',\n        options: [\n            {\n                value: 'high',\n                label: 'Sangat percaya diri',\n                weight: 1\n            },\n            {\n                value: 'medium',\n                label: 'Cukup percaya diri',\n                weight: 2\n            },\n            {\n                value: 'low',\n                label: 'Kurang percaya diri',\n                weight: 3\n            }\n        ]\n    }\n];\n// Analyze psychological profile from assessment answers\nfunction analyzePsychologicalProfile(answers) {\n    const anxietyLevel = answers.anxiety_level || 'moderate';\n    const communicationStyle = answers.communication_preference || 'gentle';\n    const personalityType = answers.personality_type || 'ambivert';\n    // Determine preferred pace based on anxiety and personality\n    let preferredPace = 'normal';\n    if (anxietyLevel === 'high' || personalityType === 'introvert') {\n        preferredPace = 'slow';\n    } else if (anxietyLevel === 'low' && personalityType === 'extrovert') {\n        preferredPace = 'fast';\n    }\n    // Determine if needs encouragement\n    const needsEncouragement = anxietyLevel === 'high' || answers.interview_experience === 'first_time' || answers.language_confidence === 'low';\n    return {\n        anxietyLevel,\n        communicationStyle,\n        personalityType,\n        preferredPace,\n        needsEncouragement,\n        culturalBackground: 'indonesian',\n        languageConfidence: answers.language_confidence || 'medium',\n        previousInterviewExperience: answers.interview_experience !== 'first_time'\n    };\n}\n// Generate adaptive prompts based on psychological profile\nfunction generateAdaptivePrompts(profile) {\n    let systemPrompt = `Anda adalah AI interviewer yang sangat memahami psikologi kandidat. `;\n    let welcomeMessage = '';\n    let encouragementMessages = [];\n    let paceInstructions = '';\n    // Adapt based on anxiety level\n    switch(profile.anxietyLevel){\n        case 'high':\n            systemPrompt += `Kandidat memiliki tingkat kecemasan tinggi. Gunakan nada yang sangat menenangkan, berikan banyak positive reinforcement, dan jangan terburu-buru. `;\n            welcomeMessage = `Halo! Saya senang sekali bisa berbicara dengan Anda hari ini. Tidak perlu khawatir, ini adalah percakapan santai untuk saling mengenal. Tarik napas dalam-dalam, dan mari kita mulai dengan rileks. 😊`;\n            encouragementMessages = [\n                \"Jawaban Anda sangat bagus! Anda melakukannya dengan baik.\",\n                \"Saya bisa merasakan antusiasme Anda. Terus seperti itu!\",\n                \"Tidak apa-apa jika perlu waktu untuk berpikir. Saya akan menunggu.\",\n                \"Anda sudah menunjukkan kemampuan yang luar biasa sejauh ini.\"\n            ];\n            paceInstructions = `Berikan jeda 3-5 detik setelah setiap pertanyaan. Gunakan kalimat pendek dan jelas.`;\n            break;\n        case 'moderate':\n            systemPrompt += `Kandidat memiliki tingkat kecemasan sedang. Berikan dukungan yang seimbang dan maintain positive energy. `;\n            welcomeMessage = `Halo! Senang bertemu dengan Anda. Saya yakin ini akan menjadi percakapan yang menyenangkan. Mari kita mulai! 🌟`;\n            encouragementMessages = [\n                \"Bagus sekali! Anda menjelaskan dengan sangat jelas.\",\n                \"Saya suka cara Anda berpikir tentang hal ini.\",\n                \"Pengalaman yang Anda ceritakan sangat menarik.\",\n                \"Anda memiliki perspektif yang unik dan berharga.\"\n            ];\n            paceInstructions = `Gunakan pace normal dengan sesekali memberikan positive feedback.`;\n            break;\n        case 'low':\n            systemPrompt += `Kandidat terlihat percaya diri. Anda bisa lebih direct dan challenging dalam pertanyaan. `;\n            welcomeMessage = `Halo! Saya siap untuk mendengar cerita menarik dari Anda. Mari kita mulai wawancara ini! 🚀`;\n            encouragementMessages = [\n                \"Excellent! Jawaban yang sangat komprehensif.\",\n                \"Saya terkesan dengan pengalaman Anda.\",\n                \"Anda memiliki pemahaman yang mendalam tentang topik ini.\",\n                \"Leadership quality Anda terlihat jelas dari cerita tadi.\"\n            ];\n            paceInstructions = `Gunakan pace yang lebih cepat dan pertanyaan yang lebih menantang.`;\n            break;\n    }\n    // Adapt based on communication style\n    switch(profile.communicationStyle){\n        case 'direct':\n            systemPrompt += `Kandidat menyukai komunikasi langsung. Gunakan pertanyaan yang to the point tanpa basa-basi berlebihan. `;\n            break;\n        case 'gentle':\n            systemPrompt += `Kandidat menyukai komunikasi yang lembut. Gunakan pendekatan yang soft dan bertahap. `;\n            break;\n        case 'encouraging':\n            systemPrompt += `Kandidat membutuhkan banyak motivasi. Berikan banyak positive reinforcement dan semangat. `;\n            break;\n        case 'formal':\n            systemPrompt += `Kandidat menyukai komunikasi formal. Gunakan bahasa yang profesional dan struktur yang jelas. `;\n            break;\n    }\n    // Adapt based on personality type\n    switch(profile.personalityType){\n        case 'introvert':\n            systemPrompt += `Kandidat adalah introvert. Berikan waktu untuk berpikir, jangan interrupt, dan hargai kedalaman jawaban mereka. `;\n            paceInstructions += ` Berikan extra time untuk reflection.`;\n            break;\n        case 'extrovert':\n            systemPrompt += `Kandidat adalah extrovert. Mereka suka berinteraksi aktif, jadi berikan energy yang matching dan follow-up questions. `;\n            break;\n        case 'ambivert':\n            systemPrompt += `Kandidat adalah ambivert. Adaptasi dengan situasi dan baca cues dari respons mereka. `;\n            break;\n    }\n    // Add language confidence adaptation\n    if (profile.languageConfidence === 'low') {\n        systemPrompt += `Kandidat kurang percaya diri dengan bahasa. Gunakan bahasa yang sederhana, berikan waktu extra, dan jangan fokus pada grammar mistakes. Fokus pada konten, bukan pada kesempurnaan bahasa. `;\n        encouragementMessages.push(\"Bahasa Anda sudah sangat baik dan mudah dipahami.\");\n        encouragementMessages.push(\"Yang penting adalah ide dan pengalaman Anda, bukan kesempurnaan bahasa.\");\n    }\n    // Add cultural sensitivity for Indonesian context\n    if (profile.culturalBackground === 'indonesian') {\n        systemPrompt += `Kandidat adalah orang Indonesia. Gunakan pendekatan yang sesuai dengan budaya Indonesia:\n    - Hargai nilai kesopanan dan kerendahan hati\n    - Jangan terlalu direct jika kandidat terlihat tidak nyaman\n    - Berikan apresiasi untuk pencapaian sekecil apapun\n    - Gunakan bahasa yang hangat dan bersahabat\n    - Pahami bahwa kandidat mungkin tidak suka terlalu menonjolkan diri (budaya tidak sombong)\n    `;\n        // Add Indonesian-specific encouragement\n        encouragementMessages.push(\"Pengalaman yang Anda bagikan sangat berharga.\");\n        encouragementMessages.push(\"Saya bisa merasakan dedikasi Anda dalam bekerja.\");\n        encouragementMessages.push(\"Terima kasih sudah berbagi dengan jujur dan terbuka.\");\n    }\n    // Add first-time interview adaptation\n    if (!profile.previousInterviewExperience) {\n        systemPrompt += `Ini adalah pengalaman wawancara pertama kandidat. Berikan guidance dan explain process dengan jelas. `;\n        welcomeMessage += ` Karena ini mungkin pengalaman wawancara pertama Anda, saya akan memandu Anda step by step. Tidak perlu khawatir!`;\n    }\n    return {\n        systemPrompt,\n        welcomeMessage,\n        encouragementMessages,\n        paceInstructions\n    };\n}\n// Detect stress indicators from speech patterns (placeholder for future implementation)\nfunction analyzeStressIndicators(audioData) {\n    // This would integrate with speech analysis APIs in the future\n    // For now, return mock data based on realistic patterns\n    return Promise.resolve({\n        speechRate: 150,\n        pauseFrequency: 8,\n        voiceShaking: false,\n        repetitiveWords: 2,\n        confidenceLevel: 75 // 0-100 scale\n    });\n}\n// Generate real-time encouragement based on stress indicators\nfunction generateRealTimeEncouragement(stressIndicators, profile) {\n    if (stressIndicators.confidenceLevel < 50) {\n        if (profile.needsEncouragement) {\n            return \"Anda sedang melakukan dengan baik. Tarik napas dalam-dalam dan lanjutkan dengan tenang.\";\n        }\n    }\n    if (stressIndicators.speechRate > 200) {\n        return \"Tidak perlu terburu-buru. Saya akan mendengarkan dengan sabar.\";\n    }\n    if (stressIndicators.pauseFrequency > 15) {\n        return \"Tidak apa-apa jika perlu waktu untuk berpikir. Saya akan menunggu.\";\n    }\n    return null // No intervention needed\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/psychological-service.ts\n");

/***/ })

};
;
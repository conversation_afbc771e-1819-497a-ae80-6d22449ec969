"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+groq@1.2.9_zod@3.25.67";
exports.ids = ["vendor-chunks/@ai-sdk+groq@1.2.9_zod@3.25.67"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+groq@1.2.9_zod@3.25.67/node_modules/@ai-sdk/groq/dist/index.mjs":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+groq@1.2.9_zod@3.25.67/node_modules/@ai-sdk/groq/dist/index.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGroq: () => (/* binding */ createGroq),\n/* harmony export */   groq: () => (/* binding */ groq)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.67/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/.pnpm/zod@3.25.67/node_modules/zod/dist/esm/index.js\");\n// src/groq-provider.ts\n\n\n\n// src/groq-chat-language-model.ts\n\n\n\n\n// src/convert-to-groq-chat-messages.ts\n\n\nfunction convertToGroqChatMessages(prompt) {\n  const messages = [];\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        messages.push({ role: \"system\", content });\n        break;\n      }\n      case \"user\": {\n        if (content.length === 1 && content[0].type === \"text\") {\n          messages.push({ role: \"user\", content: content[0].text });\n          break;\n        }\n        messages.push({\n          role: \"user\",\n          content: content.map((part) => {\n            var _a;\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"text\", text: part.text };\n              }\n              case \"image\": {\n                return {\n                  type: \"image_url\",\n                  image_url: {\n                    url: part.image instanceof URL ? part.image.toString() : `data:${(_a = part.mimeType) != null ? _a : \"image/jpeg\"};base64,${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.convertUint8ArrayToBase64)(part.image)}`\n                  }\n                };\n              }\n              case \"file\": {\n                throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.UnsupportedFunctionalityError({\n                  functionality: \"File content parts in user messages\"\n                });\n              }\n            }\n          })\n        });\n        break;\n      }\n      case \"assistant\": {\n        let text = \"\";\n        const toolCalls = [];\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args)\n                }\n              });\n              break;\n            }\n          }\n        }\n        messages.push({\n          role: \"assistant\",\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : void 0\n        });\n        break;\n      }\n      case \"tool\": {\n        for (const toolResponse of content) {\n          messages.push({\n            role: \"tool\",\n            tool_call_id: toolResponse.toolCallId,\n            content: JSON.stringify(toolResponse.result)\n          });\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return messages;\n}\n\n// src/get-response-metadata.ts\nfunction getResponseMetadata({\n  id,\n  model,\n  created\n}) {\n  return {\n    id: id != null ? id : void 0,\n    modelId: model != null ? model : void 0,\n    timestamp: created != null ? new Date(created * 1e3) : void 0\n  };\n}\n\n// src/groq-error.ts\n\n\nvar groqErrorDataSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  error: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n  })\n});\nvar groqFailedResponseHandler = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonErrorResponseHandler)({\n  errorSchema: groqErrorDataSchema,\n  errorToMessage: (data) => data.error.message\n});\n\n// src/groq-prepare-tools.ts\n\nfunction prepareTools({\n  mode\n}) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings };\n  }\n  const toolChoice = mode.toolChoice;\n  const groqTools = [];\n  for (const tool of tools) {\n    if (tool.type === \"provider-defined\") {\n      toolWarnings.push({ type: \"unsupported-tool\", tool });\n    } else {\n      groqTools.push({\n        type: \"function\",\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters\n        }\n      });\n    }\n  }\n  if (toolChoice == null) {\n    return { tools: groqTools, tool_choice: void 0, toolWarnings };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: groqTools, tool_choice: type, toolWarnings };\n    case \"tool\":\n      return {\n        tools: groqTools,\n        tool_choice: {\n          type: \"function\",\n          function: {\n            name: toolChoice.toolName\n          }\n        },\n        toolWarnings\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/map-groq-finish-reason.ts\nfunction mapGroqFinishReason(finishReason) {\n  switch (finishReason) {\n    case \"stop\":\n      return \"stop\";\n    case \"length\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    case \"function_call\":\n    case \"tool_calls\":\n      return \"tool-calls\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/groq-chat-language-model.ts\nvar GroqChatLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.supportsStructuredOutputs = false;\n    this.defaultObjectGenerationMode = \"json\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get supportsImageUrls() {\n    return !this.settings.downloadImages;\n  }\n  getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    stream,\n    providerMetadata\n  }) {\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (responseFormat != null && responseFormat.type === \"json\" && responseFormat.schema != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format schema is not supported\"\n      });\n    }\n    const groqOptions = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.parseProviderOptions)({\n      provider: \"groq\",\n      providerOptions: providerMetadata,\n      schema: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        reasoningFormat: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\"parsed\", \"raw\", \"hidden\"]).nullish()\n      })\n    });\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      stop: stopSequences,\n      seed,\n      // response format:\n      response_format: (\n        // json object response format is not supported for streaming:\n        stream === false && (responseFormat == null ? void 0 : responseFormat.type) === \"json\" ? { type: \"json_object\" } : void 0\n      ),\n      // provider options:\n      reasoning_format: groqOptions == null ? void 0 : groqOptions.reasoningFormat,\n      // messages:\n      messages: convertToGroqChatMessages(prompt)\n    };\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, toolWarnings } = prepareTools({ mode });\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice\n          },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: (\n              // json object response format is not supported for streaming:\n              stream === false ? { type: \"json_object\" } : void 0\n            )\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: {\n              type: \"function\",\n              function: { name: mode.tool.name }\n            },\n            tools: [\n              {\n                type: \"function\",\n                function: {\n                  name: mode.tool.name,\n                  description: mode.tool.description,\n                  parameters: mode.tool.parameters\n                }\n              }\n            ]\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const { args, warnings } = this.getArgs({ ...options, stream: false });\n    const body = JSON.stringify(args);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: groqFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonResponseHandler)(\n        groqChatResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n    return {\n      text: (_a = choice.message.content) != null ? _a : void 0,\n      reasoning: (_b = choice.message.reasoning) != null ? _b : void 0,\n      toolCalls: (_c = choice.message.tool_calls) == null ? void 0 : _c.map((toolCall) => {\n        var _a2;\n        return {\n          toolCallType: \"function\",\n          toolCallId: (_a2 = toolCall.id) != null ? _a2 : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n          toolName: toolCall.function.name,\n          args: toolCall.function.arguments\n        };\n      }),\n      finishReason: mapGroqFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: (_e = (_d = response.usage) == null ? void 0 : _d.prompt_tokens) != null ? _e : NaN,\n        completionTokens: (_g = (_f = response.usage) == null ? void 0 : _f.completion_tokens) != null ? _g : NaN\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings } = this.getArgs({ ...options, stream: true });\n    const body = JSON.stringify({ ...args, stream: true });\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        stream: true\n      },\n      failedResponseHandler: groqFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createEventSourceResponseHandler)(groqChatChunkSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const toolCalls = [];\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: void 0,\n      completionTokens: void 0\n    };\n    let isFirstChunk = true;\n    let providerMetadata;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (((_a = value.x_groq) == null ? void 0 : _a.usage) != null) {\n              usage = {\n                promptTokens: (_b = value.x_groq.usage.prompt_tokens) != null ? _b : void 0,\n                completionTokens: (_c = value.x_groq.usage.completion_tokens) != null ? _c : void 0\n              };\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapGroqFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.delta) == null) {\n              return;\n            }\n            const delta = choice.delta;\n            if (delta.reasoning != null && delta.reasoning.length > 0) {\n              controller.enqueue({\n                type: \"reasoning\",\n                textDelta: delta.reasoning\n              });\n            }\n            if (delta.content != null && delta.content.length > 0) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: delta.content\n              });\n            }\n            if (delta.tool_calls != null) {\n              for (const toolCallDelta of delta.tool_calls) {\n                const index = toolCallDelta.index;\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`\n                    });\n                  }\n                  if (toolCallDelta.id == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`\n                    });\n                  }\n                  if (((_d = toolCallDelta.function) == null ? void 0 : _d.name) == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`\n                    });\n                  }\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: (_e = toolCallDelta.function.arguments) != null ? _e : \"\"\n                    },\n                    hasFinished: false\n                  };\n                  const toolCall2 = toolCalls[index];\n                  if (((_f = toolCall2.function) == null ? void 0 : _f.name) != null && ((_g = toolCall2.function) == null ? void 0 : _g.arguments) != null) {\n                    if (toolCall2.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: \"tool-call-delta\",\n                        toolCallType: \"function\",\n                        toolCallId: toolCall2.id,\n                        toolName: toolCall2.function.name,\n                        argsTextDelta: toolCall2.function.arguments\n                      });\n                    }\n                    if ((0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.isParsableJson)(toolCall2.function.arguments)) {\n                      controller.enqueue({\n                        type: \"tool-call\",\n                        toolCallType: \"function\",\n                        toolCallId: (_h = toolCall2.id) != null ? _h : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n                        toolName: toolCall2.function.name,\n                        args: toolCall2.function.arguments\n                      });\n                      toolCall2.hasFinished = true;\n                    }\n                  }\n                  continue;\n                }\n                const toolCall = toolCalls[index];\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n                if (((_i = toolCallDelta.function) == null ? void 0 : _i.arguments) != null) {\n                  toolCall.function.arguments += (_k = (_j = toolCallDelta.function) == null ? void 0 : _j.arguments) != null ? _k : \"\";\n                }\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: (_l = toolCallDelta.function.arguments) != null ? _l : \"\"\n                });\n                if (((_m = toolCall.function) == null ? void 0 : _m.name) != null && ((_n = toolCall.function) == null ? void 0 : _n.arguments) != null && (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.isParsableJson)(toolCall.function.arguments)) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: (_o = toolCall.id) != null ? _o : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n          flush(controller) {\n            var _a, _b;\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              usage: {\n                promptTokens: (_a = usage.promptTokens) != null ? _a : NaN,\n                completionTokens: (_b = usage.completionTokens) != null ? _b : NaN\n              },\n              ...providerMetadata != null ? { providerMetadata } : {}\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body }\n    };\n  }\n};\nvar groqChatResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n  created: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      message: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        content: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n        reasoning: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n        tool_calls: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(\n          zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n            type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"function\"),\n            function: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n              name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n              arguments: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n            })\n          })\n        ).nullish()\n      }),\n      index: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish()\n    })\n  ),\n  usage: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().nullish(),\n    completion_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().nullish()\n  }).nullish()\n});\nvar groqChatChunkSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n    created: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().nullish(),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(\n      zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        delta: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n          content: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n          reasoning: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n          tool_calls: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(\n            zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n              index: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n              id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n              type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"function\").optional(),\n              function: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n                name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n                arguments: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish()\n              })\n            })\n          ).nullish()\n        }).nullish(),\n        finish_reason: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullable().optional(),\n        index: zod__WEBPACK_IMPORTED_MODULE_0__.z.number()\n      })\n    ),\n    x_groq: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      usage: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().nullish(),\n        completion_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().nullish()\n      }).nullish()\n    }).nullish()\n  }),\n  groqErrorDataSchema\n]);\n\n// src/groq-transcription-model.ts\n\n\nvar groqProviderOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  language: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n  prompt: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n  responseFormat: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().nullish(),\n  temperature: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(1).nullish(),\n  timestampGranularities: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).nullish()\n});\nvar GroqTranscriptionModel = class {\n  constructor(modelId, config) {\n    this.modelId = modelId;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    audio,\n    mediaType,\n    providerOptions\n  }) {\n    var _a, _b, _c, _d, _e;\n    const warnings = [];\n    const groqOptions = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.parseProviderOptions)({\n      provider: \"groq\",\n      providerOptions,\n      schema: groqProviderOptionsSchema\n    });\n    const formData = new FormData();\n    const blob = audio instanceof Uint8Array ? new Blob([audio]) : new Blob([(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.convertBase64ToUint8Array)(audio)]);\n    formData.append(\"model\", this.modelId);\n    formData.append(\"file\", new File([blob], \"audio\", { type: mediaType }));\n    if (groqOptions) {\n      const transcriptionModelOptions = {\n        language: (_a = groqOptions.language) != null ? _a : void 0,\n        prompt: (_b = groqOptions.prompt) != null ? _b : void 0,\n        response_format: (_c = groqOptions.responseFormat) != null ? _c : void 0,\n        temperature: (_d = groqOptions.temperature) != null ? _d : void 0,\n        timestamp_granularities: (_e = groqOptions.timestampGranularities) != null ? _e : void 0\n      };\n      for (const key in transcriptionModelOptions) {\n        const value = transcriptionModelOptions[key];\n        if (value !== void 0) {\n          formData.append(key, String(value));\n        }\n      }\n    }\n    return {\n      formData,\n      warnings\n    };\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e;\n    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();\n    const { formData, warnings } = this.getArgs(options);\n    const {\n      value: response,\n      responseHeaders,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postFormDataToApi)({\n      url: this.config.url({\n        path: \"/audio/transcriptions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(this.config.headers(), options.headers),\n      formData,\n      failedResponseHandler: groqFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonResponseHandler)(\n        groqTranscriptionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      text: response.text,\n      segments: (_e = (_d = response.segments) == null ? void 0 : _d.map((segment) => ({\n        text: segment.text,\n        startSecond: segment.start,\n        endSecond: segment.end\n      }))) != null ? _e : [],\n      language: response.language,\n      durationInSeconds: response.duration,\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse\n      }\n    };\n  }\n};\nvar groqTranscriptionResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n  task: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n  language: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n  duration: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n  text: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n  segments: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(\n    zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n      id: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      seek: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      start: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      end: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      text: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n      tokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.number()),\n      temperature: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      avg_logprob: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      compression_ratio: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n      no_speech_prob: zod__WEBPACK_IMPORTED_MODULE_0__.z.number()\n    })\n  ),\n  x_groq: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n  })\n});\n\n// src/groq-provider.ts\nfunction createGroq(options = {}) {\n  var _a;\n  const baseURL = (_a = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.withoutTrailingSlash)(options.baseURL)) != null ? _a : \"https://api.groq.com/openai/v1\";\n  const getHeaders = () => ({\n    Authorization: `Bearer ${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.loadApiKey)({\n      apiKey: options.apiKey,\n      environmentVariableName: \"GROQ_API_KEY\",\n      description: \"Groq\"\n    })}`,\n    ...options.headers\n  });\n  const createChatModel = (modelId, settings = {}) => new GroqChatLanguageModel(modelId, settings, {\n    provider: \"groq.chat\",\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createLanguageModel = (modelId, settings) => {\n    if (new.target) {\n      throw new Error(\n        \"The Groq model function cannot be called with the new keyword.\"\n      );\n    }\n    return createChatModel(modelId, settings);\n  };\n  const createTranscriptionModel = (modelId) => {\n    return new GroqTranscriptionModel(modelId, {\n      provider: \"groq.transcription\",\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch\n    });\n  };\n  const provider = function(modelId, settings) {\n    return createLanguageModel(modelId, settings);\n  };\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.textEmbeddingModel = (modelId) => {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_2__.NoSuchModelError({ modelId, modelType: \"textEmbeddingModel\" });\n  };\n  provider.transcription = createTranscriptionModel;\n  return provider;\n}\nvar groq = createGroq();\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+groq@1.2.9_zod@3.25.67/node_modules/@ai-sdk/groq/dist/index.mjs\n");

/***/ })

};
;
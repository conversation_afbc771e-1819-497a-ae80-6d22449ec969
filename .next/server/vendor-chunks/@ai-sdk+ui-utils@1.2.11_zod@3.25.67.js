"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.25.67";
exports.ids = ["vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.25.67"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.25.67/node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.25.67/node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.67/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(ssr)/./node_modules/.pnpm/zod-to-json-schema@3.24.5_zod@3.25.67/node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n      message.parts.push({ type: \"step-start\" });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage,\n  requestType = \"generate\"\n}) {\n  var _a, _b, _c;\n  const request = requestType === \"resume\" ? fetch2(`${api}?chatId=${body.id}`, {\n    method: \"GET\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }) : fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_b = abortController == null ? void 0 : abortController()) == null ? void 0 : _b.signal,\n    credentials\n  });\n  const response = await request.catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_c = await response.text()) != null ? _c : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (globalThis.FileList && attachmentsFromOptions instanceof globalThis.FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  if (message.role !== \"assistant\") {\n    return false;\n  }\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === \"step-start\" ? index : lastIndex;\n  }, -1);\n  const lastStepToolInvocations = message.parts.slice(lastStepStartIndex + 1).filter((part) => part.type === \"tool-invocation\");\n  return lastStepToolInvocations.length > 0 && lastStepToolInvocations.every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.25.67/node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ })

};
;
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.25.67";
exports.ids = ["vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.25.67"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.67/node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.67/node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asValidator: () => (/* binding */ asValidator),\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncIteratorToReadableStream: () => (/* binding */ convertAsyncIteratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createBinaryResponseHandler: () => (/* binding */ createBinaryResponseHandler),\n/* harmony export */   createEventSourceParserStream: () => (/* binding */ createEventSourceParserStream),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createIdGenerator: () => (/* binding */ createIdGenerator),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   createStatusCodeErrorResponseHandler: () => (/* binding */ createStatusCodeErrorResponseHandler),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getFromApi: () => (/* binding */ getFromApi),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadOptionalSetting: () => (/* binding */ loadOptionalSetting),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   parseProviderOptions: () => (/* binding */ parseProviderOptions),\n/* harmony export */   postFormDataToApi: () => (/* binding */ postFormDataToApi),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   removeUndefinedEntries: () => (/* binding */ removeUndefinedEntries),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(ssr)/./node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/.pnpm/secure-json-parse@2.7.0/node_modules/secure-json-parse/index.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-iterator-to-readable-stream.ts\nfunction convertAsyncIteratorToReadableStream(iterator) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/delay.ts\nasync function delay(delayInMs) {\n  return delayInMs == null ? Promise.resolve() : new Promise((resolve2) => setTimeout(resolve2, delayInMs));\n}\n\n// src/event-source-parser-stream.ts\nfunction createEventSourceParserStream() {\n  let buffer = \"\";\n  let event = void 0;\n  let data = [];\n  let lastEventId = void 0;\n  let retry = void 0;\n  function parseLine(line, controller) {\n    if (line === \"\") {\n      dispatchEvent(controller);\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      return;\n    }\n    const colonIndex = line.indexOf(\":\");\n    if (colonIndex === -1) {\n      handleField(line, \"\");\n      return;\n    }\n    const field = line.slice(0, colonIndex);\n    const valueStart = colonIndex + 1;\n    const value = valueStart < line.length && line[valueStart] === \" \" ? line.slice(valueStart + 1) : line.slice(valueStart);\n    handleField(field, value);\n  }\n  function dispatchEvent(controller) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join(\"\\n\"),\n        id: lastEventId,\n        retry\n      });\n      data = [];\n      event = void 0;\n      retry = void 0;\n    }\n  }\n  function handleField(field, value) {\n    switch (field) {\n      case \"event\":\n        event = value;\n        break;\n      case \"data\":\n        data.push(value);\n        break;\n      case \"id\":\n        lastEventId = value;\n        break;\n      case \"retry\":\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n      buffer = incompleteLine;\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    }\n  });\n}\nfunction splitLines(buffer, chunk) {\n  const lines = [];\n  let currentLine = buffer;\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n    if (char === \"\\n\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n    } else if (char === \"\\r\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n      if (chunk[i] === \"\\n\") {\n        i++;\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n  return { lines, incompleteLine: currentLine };\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\n\nvar createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  separator = \"-\"\n} = {}) => {\n  const generator = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(alphabet, defaultSize);\n  if (prefix == null) {\n    return generator;\n  }\n  if (alphabet.includes(separator)) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"separator\",\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`\n    });\n  }\n  return (size) => `${prefix}${separator}${generator(size)}`;\n};\nvar generateId = createIdGenerator();\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/get-from-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/get-from-api.ts\nvar getOriginalFetch = () => globalThis.fetch;\nvar getFromApi = async ({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"GET\",\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {}\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {}\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {}\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {}\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {}\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-optional-setting.ts\nfunction loadOptionalSetting({\n  settingValue,\n  environmentVariableName\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null || typeof process === \"undefined\") {\n    return void 0;\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null || typeof settingValue !== \"string\") {\n    return void 0;\n  }\n  return settingValue;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol.for(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction asValidator(value) {\n  return isValidator(value) ? value : zodValidator(value);\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema\n}) {\n  const validator2 = asValidator(schema);\n  try {\n    if (validator2.validate == null) {\n      return { success: true, value };\n    }\n    const result = validator2.validate(value);\n    if (result.success) {\n      return result;\n    }\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isInstance(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return { success: true, value, rawValue: value };\n    }\n    const validationResult = safeValidateTypes({ value, schema });\n    return validationResult.success ? { ...validationResult, rawValue: value } : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// src/parse-provider-options.ts\n\nfunction parseProviderOptions({\n  provider,\n  providerOptions,\n  schema\n}) {\n  if ((providerOptions == null ? void 0 : providerOptions[provider]) == null) {\n    return void 0;\n  }\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema\n  });\n  if (!parsedProviderOptions.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"providerOptions\",\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error\n    });\n  }\n  return parsedProviderOptions.value;\n}\n\n// src/post-to-api.ts\n\nvar getOriginalFetch2 = () => globalThis.fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postFormDataToApi = async ({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers,\n  body: {\n    content: formData,\n    values: Object.fromEntries(formData.entries())\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch2()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/resolve.ts\nasync function resolve(value) {\n  if (typeof value === \"function\") {\n    value = value();\n  }\n  return Promise.resolve(value);\n}\n\n// src/response-handler.ts\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value,\n    rawValue: parsedResult.rawValue\n  };\n};\nvar createBinaryResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (!response.body) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Response body is empty\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0\n    });\n  }\n  try {\n    const buffer = await response.arrayBuffer();\n    return {\n      responseHeaders,\n      value: new Uint8Array(buffer)\n    };\n  } catch (error) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Failed to read response as array buffer\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0,\n      cause: error\n    });\n  }\n};\nvar createStatusCodeErrorResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  const responseBody = await response.text();\n  return {\n    responseHeaders,\n    value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: response.statusText,\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody\n    })\n  };\n};\n\n// src/uint8-utils.ts\nvar { btoa, atob } = globalThis;\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.67/node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ })

};
;
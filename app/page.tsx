"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, User, Bo<PERSON>, Loader2, Download, Upload } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { processUserInput, speechToText, textToSpeech, VOICE_OPTIONS, VoiceGender } from "@/lib/ai-service"

type Message = {
  role: "user" | "assistant"
  content: string
  audioUrl?: string
}

export default function InterviewPage() {
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [recordingTime, setRecordingTime] = useState(0)
  const [recordingMode, setRecordingMode] = useState<"live" | "upload">("live")
  const [voiceGender, setVoiceGender] = useState<VoiceGender>('female')

  // Refs for audio recording
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize with a greeting when the component mounts
  useEffect(() => {
    if (!isInitialized) {
      const initializeInterview = async () => {
        setIsProcessing(true)
        try {
          console.log("Initializing interview...")

          // Check if API key is available
          const hasApiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY || process.env.GROQ_API_KEY
          if (!hasApiKey) {
            throw new Error("API key Groq tidak ditemukan. Pastikan GROQ_API_KEY sudah dikonfigurasi di .env.local")
          }

          const initialPrompt =
            "Perkenalkan diri Anda sebagai AI interviewer dan mulai wawancara kerja dalam bahasa Indonesia."

          console.log("Sending initial prompt to AI...")
          const initialResponse = await processUserInput(initialPrompt)
          console.log("Received initial AI response:", initialResponse)

          // Convert the initial response to speech (optional, skip if fails)
          let audioUrl = ""
          try {
            console.log("Converting initial response to speech...")
            audioUrl = await textToSpeech(initialResponse, voiceGender)
          } catch (speechError) {
            console.warn("Text-to-speech failed, continuing without audio:", speechError)
          }

          setMessages([
            {
              role: "assistant",
              content: initialResponse,
              audioUrl: audioUrl,
            },
          ])
          console.log("Interview initialized successfully")
        } catch (error) {
          console.error("Error initializing interview:", error)
          // Show a more detailed error message
          if (error instanceof Error) {
            setError(`Gagal memulai wawancara: ${error.message}. Silakan periksa konfigurasi API key.`)
          } else {
            setError("Gagal memulai wawancara. Silakan muat ulang halaman.")
          }
        } finally {
          setIsProcessing(false)
          setIsInitialized(true)
        }
      }

      initializeInterview()
    }
  }, [isInitialized])

  // Start the recording timer
  const startTimer = () => {
    setRecordingTime(0)
    timerRef.current = setInterval(() => {
      setRecordingTime((prevTime) => prevTime + 1)
    }, 1000)
  }

  // Stop the recording timer
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }

  // Start recording using MediaRecorder API
  const startRecording = async () => {
    setIsRecording(true)
    audioChunksRef.current = []

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      // Create a new MediaRecorder instance
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder

      // Set up event handlers
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      // Start recording
      mediaRecorder.start()
      startTimer()
    } catch (error) {
      console.error("Error accessing microphone:", error)
      setError("Gagal mengakses mikrofon. Pastikan Anda memberikan izin.")
      setIsRecording(false)
    }
  }

  // Stop recording and process the audio
  const stopRecording = async () => {
    setIsRecording(false)
    stopTimer()

    if (!mediaRecorderRef.current) {
      setError("Tidak ada rekaman yang sedang berlangsung.")
      return
    }

    try {
      // Create a Promise that resolves when the MediaRecorder stops
      const recordingData = await new Promise<Blob>((resolve) => {
        mediaRecorderRef.current!.onstop = () => {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' })
          resolve(audioBlob)
        }
        mediaRecorderRef.current!.stop()
      })

      // Stop all tracks in the stream
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop())

      // Process the recording
      await processRecording(recordingData)
    } catch (error) {
      console.error("Error stopping recording:", error)
      setError("Terjadi kesalahan saat menghentikan rekaman. Silakan coba lagi.")
      setIsProcessing(false)
    }
  }

  // Process an audio file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setAudioFile(file)
    }
  }

  // Process the uploaded audio file
  const processAudioFile = async () => {
    if (!audioFile) {
      setError("Silakan pilih file audio terlebih dahulu.")
      return
    }

    setIsProcessing(true)
    try {
      await processRecording(audioFile)
    } catch (error) {
      console.error("Error processing audio file:", error)
      setError("Terjadi kesalahan saat memproses file audio. Silakan coba lagi.")
      setIsProcessing(false)
    }
  }

  // Common function to process audio (either from recording or file upload)
  const processRecording = async (audioData: Blob | File) => {
    setIsProcessing(true)

    try {
      console.log("Starting to process audio recording...")

      // Convert speech to text using Groq's Whisper API
      console.log("Sending audio to speech-to-text service...")
      const transcript = await speechToText(audioData)
      console.log("Received transcript:", transcript)

      if (!transcript || transcript.trim() === "") {
        throw new Error("Tidak ada teks yang terdeteksi dalam rekaman. Silakan coba lagi dengan suara yang lebih jelas.")
      }

      // Add user message
      const userMessage: Message = {
        role: "user",
        content: transcript,
      }

      setMessages((prev) => [...prev, userMessage])

      // Process with AI and get response
      console.log("Sending transcript to AI for processing...")
      const aiResponse = await processUserInput(transcript)
      console.log("Received AI response:", aiResponse)

      // Convert AI response to speech
      console.log("Converting AI response to speech...")
      const audioUrl = await textToSpeech(aiResponse, voiceGender)

      // Add AI message
      const assistantMessage: Message = {
        role: "assistant",
        content: aiResponse,
        audioUrl: audioUrl,
      }

      setMessages((prev) => [...prev, assistantMessage])
      console.log("Processing complete")
    } catch (error) {
      console.error("Error processing recording:", error)
      // Show a more detailed error message
      if (error instanceof Error) {
        setError(`Terjadi kesalahan: ${error.message}`)
      } else {
        setError("Terjadi kesalahan saat memproses rekaman. Silakan coba lagi.")
      }
    } finally {
      setIsProcessing(false)
    }
  }

  // Toggle recording state
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording()
    } else {
      startRecording()
    }
  }

  // Function to play message text using text-to-speech
  const playMessage = async (text: string, audioUrl?: string) => {
    try {
      if (audioUrl) {
        // If we have a stored audio URL, play it directly
        const audio = new Audio(audioUrl)
        audio.play()
      } else {
        // Otherwise, generate new speech with current voice gender
        await textToSpeech(text, voiceGender)
      }
    } catch (error) {
      console.error("Error playing message:", error)
      setError("Gagal memutar pesan. Silakan coba lagi.")
    }
  }

  // Function to export the transcript
  const exportTranscript = () => {
    try {
      // Format the transcript
      const date = new Date().toLocaleString("id-ID")
      let transcriptContent = `AI Interview Transcript - ${date}\n\n`

      messages.forEach((message, index) => {
        const role = message.role === "assistant" ? "AI Interviewer" : "Kandidat"
        transcriptContent += `${role}: ${message.content}\n\n`
      })

      // Create a blob and download link
      const blob = new Blob([transcriptContent], { type: "text/plain;charset=utf-8" })
      const url = URL.createObjectURL(blob)

      // Create a temporary link and trigger download
      const link = document.createElement("a")
      link.href = url
      link.download = `interview-transcript-${new Date().toISOString().slice(0, 10)}.txt`
      document.body.appendChild(link)
      link.click()

      // Clean up
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error exporting transcript:", error)
      setError("Gagal mengekspor transkrip. Silakan coba lagi.")
    }
  }

  return (
    <div className="container mx-auto max-w-4xl py-8">
      <Card className="w-full">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <CardTitle className="text-2xl font-bold text-center">AI Interview System</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <p>{error}</p>
              <Button variant="outline" size="sm" className="mt-2" onClick={() => setError(null)}>
                Dismiss
              </Button>
            </div>
          )}

          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-lg font-semibold">Conversation</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={exportTranscript}
                disabled={messages.length === 0}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" /> Export Transcript
              </Button>
            </div>

            <div className="flex-1 overflow-y-auto max-h-[60vh] space-y-4 p-4 rounded-lg border">
              {messages.map((message, index) => (
                <div key={index} className={`flex ${message.role === "assistant" ? "justify-start" : "justify-end"}`}>
                  <div
                    className={`flex gap-3 max-w-[80%] ${message.role === "assistant" ? "flex-row" : "flex-row-reverse"}`}
                  >
                    <Avatar className={message.role === "assistant" ? "bg-blue-100" : "bg-gray-100"}>
                      <AvatarFallback>
                        {message.role === "assistant" ? (
                          <Bot className="h-5 w-5 text-blue-500" />
                        ) : (
                          <User className="h-5 w-5" />
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`rounded-lg p-4 ${message.role === "assistant" ? "bg-blue-100" : "bg-gray-100"}`}>
                      <p>{message.content}</p>
                      {message.role === "assistant" && (
                        <Button variant="ghost" size="sm" className="mt-2" onClick={() => playMessage(message.content, message.audioUrl)}>
                          <Play className="h-4 w-4 mr-1" /> Play
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {messages.length === 0 && !isProcessing && (
                <div className="text-center text-gray-500 py-8">Memulai wawancara...</div>
              )}

              {isProcessing && (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              )}
            </div>

            <Separator />

            <div className="flex flex-col items-center justify-center p-4 space-y-4">
              {/* Voice gender selection */}
              <div className="w-full max-w-md">
                <h3 className="text-sm font-medium mb-2 text-center">Pilih Suara AI Interviewer:</h3>
                <div className="flex space-x-2 justify-center">
                  <Button
                    variant={voiceGender === "female" ? "default" : "outline"}
                    onClick={() => setVoiceGender("female")}
                    disabled={isProcessing || isRecording}
                    className="flex items-center gap-1"
                    size="sm"
                  >
                    👩 {VOICE_OPTIONS.female.name}
                  </Button>
                  <Button
                    variant={voiceGender === "male" ? "default" : "outline"}
                    onClick={() => setVoiceGender("male")}
                    disabled={isProcessing || isRecording}
                    className="flex items-center gap-1"
                    size="sm"
                  >
                    👨 {VOICE_OPTIONS.male.name}
                  </Button>
                </div>
                <p className="text-xs text-gray-500 text-center mt-1">
                  {VOICE_OPTIONS[voiceGender].description}
                </p>
              </div>

              <Separator className="w-full max-w-md" />

              {/* Recording mode toggle */}
              <div className="flex space-x-2 mb-2">
                <Button
                  variant={recordingMode === "live" ? "default" : "outline"}
                  onClick={() => setRecordingMode("live")}
                  disabled={isProcessing || isRecording}
                  className="flex items-center gap-1"
                >
                  <Mic className="h-4 w-4" /> Rekam Langsung
                </Button>
                <Button
                  variant={recordingMode === "upload" ? "default" : "outline"}
                  onClick={() => setRecordingMode("upload")}
                  disabled={isProcessing || isRecording}
                  className="flex items-center gap-1"
                >
                  <Upload className="h-4 w-4" /> Unggah Audio
                </Button>
              </div>

              {/* Live recording UI */}
              {recordingMode === "live" && (
                <div className="text-center">
                  <Button
                    onClick={toggleRecording}
                    disabled={isProcessing}
                    size="lg"
                    className={`rounded-full h-16 w-16 ${
                      isRecording ? "bg-red-500 hover:bg-red-600" : "bg-blue-500 hover:bg-blue-600"
                    }`}
                  >
                    {isRecording ? <MicOff className="h-8 w-8" /> : <Mic className="h-8 w-8" />}
                  </Button>
                  <p className="mt-2 text-sm text-gray-500">
                    {isProcessing
                      ? "Memproses..."
                      : isRecording
                        ? `Merekam... (${recordingTime}s)`
                        : "Klik untuk mulai merekam"}
                  </p>
                </div>
              )}

              {/* File upload UI */}
              {recordingMode === "upload" && (
                <div className="flex flex-col items-center space-y-3 w-full max-w-md">
                  <div className="flex items-center space-x-2 w-full">
                    <Input
                      type="file"
                      accept="audio/*"
                      onChange={handleFileUpload}
                      disabled={isProcessing}
                      className="flex-1"
                    />
                    <Button
                      onClick={processAudioFile}
                      disabled={!audioFile || isProcessing}
                      className="whitespace-nowrap"
                    >
                      {isProcessing ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Upload className="h-4 w-4 mr-2" />
                      )}
                      Proses
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Format yang didukung: MP3, WAV, M4A, FLAC, OGG, WEBM
                  </p>
                  {audioFile && (
                    <p className="text-sm">
                      File dipilih: <span className="font-medium">{audioFile.name}</span>
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="bg-gray-50 p-4 flex justify-center">
          <p className="text-sm text-gray-500">
            {recordingMode === "live"
              ? "Klik tombol mikrofon untuk berbicara dengan AI interviewer"
              : "Unggah file audio untuk diproses oleh AI interviewer"}
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}

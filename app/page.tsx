"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Play, User, Bo<PERSON>, Loader2, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

import { processUserInput, speechToText, textToSpeech, VOICE_OPTIONS, VoiceGender } from "@/lib/ai-service"
import { PsychologicalProfile, generateAdaptivePrompts } from "@/lib/psychological-service"
import PsychologicalAssessment from "@/components/psychological-assessment"
import PsychologicalSafety from "@/components/psychological-safety"

type Message = {
  role: "user" | "assistant"
  content: string
  audioUrl?: string
}

export default function InterviewPage() {
  // State untuk menentukan apakah sudah memilih gender atau belum
  const [hasSelectedGender, setHasSelectedGender] = useState(false)
  const [voiceGender, setVoiceGender] = useState<VoiceGender>('female')

  // State untuk psychological assessment
  const [showPsychAssessment, setShowPsychAssessment] = useState(false)
  const [psychologicalProfile, setPsychologicalProfile] = useState<PsychologicalProfile | null>(null)

  // State untuk psychological safety
  const [showPsychSafety, setShowPsychSafety] = useState(false)
  const [stressLevel, setStressLevel] = useState<'low' | 'medium' | 'high'>('medium')

  // State untuk interview
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [recordingTime, setRecordingTime] = useState(0)

  // Refs untuk audio recording
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  
  // Function untuk memilih gender dan mulai psychological assessment
  const handleGenderSelection = (gender: VoiceGender) => {
    setVoiceGender(gender)
    setShowPsychAssessment(true)
  }

  // Function untuk menyelesaikan psychological assessment
  const handlePsychAssessmentComplete = (profile: PsychologicalProfile) => {
    setPsychologicalProfile(profile)
    setShowPsychAssessment(false)
    setHasSelectedGender(true)
  }

  // Initialize interview setelah gender dipilih
  useEffect(() => {
    if (hasSelectedGender && !isInitialized) {
      const initializeInterview = async () => {
        setIsProcessing(true)
        try {
          console.log("Initializing interview...")
          
          // Generate adaptive prompts based on psychological profile
          let initialPrompt = "Perkenalkan diri Anda sebagai AI interviewer dan mulai wawancara kerja dalam bahasa Indonesia."
          let welcomeMessage = ""

          if (psychologicalProfile) {
            const adaptivePrompts = generateAdaptivePrompts(psychologicalProfile)
            initialPrompt = adaptivePrompts.systemPrompt + " " + initialPrompt
            welcomeMessage = adaptivePrompts.welcomeMessage
          }

          console.log("Sending adaptive initial prompt to AI...")
          const initialResponse = await processUserInput(welcomeMessage || initialPrompt, psychologicalProfile || undefined, [])
          console.log("Received initial AI response:", initialResponse)

          // Convert the initial response to speech
          let audioUrl = ""
          try {
            console.log("Converting initial response to speech...")
            audioUrl = await textToSpeech(initialResponse, voiceGender)
          } catch (speechError) {
            console.warn("Text-to-speech failed, continuing without audio:", speechError)
          }

          setMessages([
            {
              role: "assistant",
              content: initialResponse,
              audioUrl: audioUrl,
            },
          ])
          console.log("Interview initialized successfully")
        } catch (error) {
          console.error("Error initializing interview:", error)
          if (error instanceof Error) {
            setError(`Gagal memulai wawancara: ${error.message}. Silakan periksa konfigurasi API key.`)
          } else {
            setError("Gagal memulai wawancara. Silakan muat ulang halaman.")
          }
        } finally {
          setIsProcessing(false)
          setIsInitialized(true)
        }
      }

      initializeInterview()
    }
  }, [hasSelectedGender, isInitialized, voiceGender])

  // Start the recording timer
  const startTimer = () => {
    setRecordingTime(0)
    timerRef.current = setInterval(() => {
      setRecordingTime((prevTime) => prevTime + 1)
    }, 1000)
  }

  // Stop the recording timer
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }

  // Start recording using MediaRecorder API
  const startRecording = async () => {
    setIsRecording(true)
    audioChunksRef.current = []

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      // Create a new MediaRecorder instance
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder

      // Set up event handlers
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      // Start recording
      mediaRecorder.start()
      startTimer()
    } catch (error) {
      console.error("Error accessing microphone:", error)
      setError("Gagal mengakses mikrofon. Pastikan Anda memberikan izin.")
      setIsRecording(false)
    }
  }

  // Stop recording and process the audio
  const stopRecording = async () => {
    setIsRecording(false)
    stopTimer()

    if (!mediaRecorderRef.current) {
      setError("Tidak ada rekaman yang sedang berlangsung.")
      return
    }

    try {
      // Create a Promise that resolves when the MediaRecorder stops
      const recordingData = await new Promise<Blob>((resolve) => {
        mediaRecorderRef.current!.onstop = () => {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' })
          resolve(audioBlob)
        }
        mediaRecorderRef.current!.stop()
      })

      // Stop all tracks in the stream
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop())

      // Process the recording
      await processRecording(recordingData)
    } catch (error) {
      console.error("Error stopping recording:", error)
      setError("Terjadi kesalahan saat menghentikan rekaman. Silakan coba lagi.")
      setIsProcessing(false)
    }
  }

  // Process audio recording
  const processRecording = async (audioData: Blob) => {
    setIsProcessing(true)

    try {
      console.log("Starting to process audio recording...")

      // Convert speech to text using Groq Whisper API
      console.log("Converting speech to text with Groq Whisper...")
      const transcript = await speechToText(audioData)
      console.log("Received transcript:", transcript)

      if (!transcript || transcript.trim() === "") {
        throw new Error("Tidak ada teks yang terdeteksi dalam rekaman. Silakan coba lagi dengan suara yang lebih jelas.")
      }

      // Add user message
      const userMessage: Message = {
        role: "user",
        content: transcript,
      }

      setMessages((prev) => [...prev, userMessage])

      // Detect stress indicators from user input
      const stressIndicators = ['um', 'eh', 'tidak tahu', 'mungkin', 'sepertinya', 'kayaknya', 'gimana ya', 'bingung', 'susah']
      const hasStressIndicators = stressIndicators.some(indicator => transcript.toLowerCase().includes(indicator))

      // Check if user needs psychological safety break
      if (hasStressIndicators && psychologicalProfile && psychologicalProfile.needsEncouragement && messages.length > 4) {
        setStressLevel('high')
        setShowPsychSafety(true)
        setIsProcessing(false)
        return
      }

      // Process with AI and get response with psychological adaptation
      console.log("Sending transcript to AI for processing...")
      const messageHistoryForAI = messages.map(m => ({ role: m.role, content: m.content }))
      const aiResponse = await processUserInput(transcript, psychologicalProfile || undefined, messageHistoryForAI)
      console.log("Received AI response:", aiResponse)

      // Convert AI response to speech
      console.log("Converting AI response to speech...")
      const audioUrl = await textToSpeech(aiResponse, voiceGender)

      // Add AI message
      const assistantMessage: Message = {
        role: "assistant",
        content: aiResponse,
        audioUrl: audioUrl,
      }

      setMessages((prev) => [...prev, assistantMessage])
      console.log("Processing complete")
    } catch (error) {
      console.error("Error processing recording:", error)
      if (error instanceof Error) {
        setError(`Terjadi kesalahan: ${error.message}`)
      } else {
        setError("Terjadi kesalahan saat memproses rekaman. Silakan coba lagi.")
      }
    } finally {
      setIsProcessing(false)
    }
  }

  // Toggle recording state
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording()
    } else {
      startRecording()
    }
  }

  // Function to play message text using text-to-speech
  const playMessage = async (text: string, audioUrl?: string) => {
    try {
      if (audioUrl) {
        // If we have a stored audio URL, play it directly
        const audio = new Audio(audioUrl)
        audio.play()
      } else {
        // Otherwise, generate new speech with current voice gender
        await textToSpeech(text, voiceGender)
      }
    } catch (error) {
      console.error("Error playing message:", error)
      setError("Gagal memutar pesan. Silakan coba lagi.")
    }
  }

  // Function to export the transcript
  const exportTranscript = () => {
    try {
      // Format the transcript
      const date = new Date().toLocaleString("id-ID")
      let transcriptContent = `AI Interview Transcript - ${date}\n\n`

      messages.forEach((message) => {
        const role = message.role === "assistant" ? "AI Interviewer" : "Kandidat"
        transcriptContent += `${role}: ${message.content}\n\n`
      })

      // Create a blob and download link
      const blob = new Blob([transcriptContent], { type: "text/plain;charset=utf-8" })
      const url = URL.createObjectURL(blob)

      // Create a temporary link and trigger download
      const link = document.createElement("a")
      link.href = url
      link.download = `interview-transcript-${new Date().toISOString().slice(0, 10)}.txt`
      document.body.appendChild(link)
      link.click()

      // Clean up
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error exporting transcript:", error)
      setError("Gagal mengekspor transkrip. Silakan coba lagi.")
    }
  }

  // Jika sedang menampilkan psychological assessment
  if (showPsychAssessment) {
    return <PsychologicalAssessment onComplete={handlePsychAssessmentComplete} />
  }

  // Jika belum memilih gender, tampilkan halaman pilihan
  if (!hasSelectedGender) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
        <div className="w-full max-w-4xl">
          {/* Header Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full mb-6">
              <Bot className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">AI Interview System</h1>
            <p className="text-xl text-gray-600 mb-2">Sistem Wawancara Kerja dengan Kecerdasan Buatan</p>
            <p className="text-gray-500">Pilih suara AI interviewer yang membuat Anda nyaman</p>
          </div>

          {/* Voice Selection Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto">
            {/* Female Voice Card */}
            <Card
              className="group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm"
              onClick={() => handleGenderSelection('female')}
            >
              <CardContent className="p-8 text-center h-full flex flex-col justify-between">
                <div>
                  <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-pink-100 to-rose-100 rounded-full flex items-center justify-center group-hover:from-pink-200 group-hover:to-rose-200 transition-all duration-300">
                    <div className="text-4xl">👩‍💼</div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">Suara Perempuan</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    Suara perempuan Indonesia yang natural dan profesional dari Google TTS
                  </p>
                  <div className="flex items-center justify-center text-sm text-gray-500 mb-6">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    Google TTS Indonesia
                  </div>
                </div>
                <Button className="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300">
                  <User className="h-5 w-5 mr-2" />
                  Pilih Suara Perempuan
                </Button>
              </CardContent>
            </Card>

            {/* Male Voice Card */}
            <Card
              className="group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm"
              onClick={() => handleGenderSelection('male')}
            >
              <CardContent className="p-8 text-center h-full flex flex-col justify-between">
                <div>
                  <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-300">
                    <div className="text-4xl">👨‍💼</div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">Suara Laki-laki</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    Suara laki-laki Indonesia yang natural dan profesional dari Google TTS
                  </p>
                  <div className="flex items-center justify-center text-sm text-gray-500 mb-6">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    Google TTS Indonesia
                  </div>
                </div>
                <Button className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300">
                  <User className="h-5 w-5 mr-2" />
                  Pilih Suara Laki-laki
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Footer Info */}
          <div className="text-center mt-12">
            <p className="text-sm text-gray-500">
              Powered by Google TTS • Groq Whisper • Gemini AI
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Interface wawancara dengan layout kiri-kanan
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Psychological Safety Break Modal */}
      <PsychologicalSafety
        isVisible={showPsychSafety}
        onClose={() => setShowPsychSafety(false)}
        onBreakComplete={() => setShowPsychSafety(false)}
        profile={psychologicalProfile || undefined}
        stressLevel={stressLevel}
      />
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">AI Interview System</h1>
            <p className="text-blue-100">Suara: {VOICE_OPTIONS[voiceGender].name}</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportTranscript}
              disabled={messages.length === 0}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            >
              <Download className="h-4 w-4 mr-1" /> Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setHasSelectedGender(false)}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            >
              Ganti Suara
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-4 h-[calc(100vh-80px)] flex flex-col">
        {/* Transcript Section (Atas) - Pesan Terbaru di Atas */}
        <Card className="h-80 mb-4">
          <CardHeader className="bg-gray-50 py-3">
            <CardTitle className="text-lg">Percakapan Interview</CardTitle>
          </CardHeader>
          <CardContent className="p-4 h-72 overflow-y-auto flex flex-col-reverse">
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <p>{error}</p>
                <Button variant="outline" size="sm" className="mt-2" onClick={() => setError(null)}>
                  Tutup
                </Button>
              </div>
            )}

            <div className="space-y-4 flex flex-col-reverse">
              {isProcessing && (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                  <span className="ml-2 text-sm text-gray-600">AI sedang memproses...</span>
                </div>
              )}

              {messages.slice().reverse().map((message, index) => (
                <div key={messages.length - 1 - index} className={`flex ${message.role === "assistant" ? "justify-start" : "justify-end"}`}>
                  <div
                    className={`flex gap-3 max-w-[85%] ${message.role === "assistant" ? "flex-row" : "flex-row-reverse"}`}
                  >
                    <Avatar className={`w-10 h-10 ${message.role === "assistant" ? "bg-blue-100" : "bg-green-100"}`}>
                      <AvatarFallback>
                        {message.role === "assistant" ? (
                          <Bot className="h-5 w-5 text-blue-500" />
                        ) : (
                          <User className="h-5 w-5 text-green-500" />
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`rounded-lg p-4 text-sm shadow-sm ${message.role === "assistant" ? "bg-blue-50 border-l-4 border-blue-400" : "bg-green-50 border-l-4 border-green-400"}`}>
                      <div className={`text-xs font-medium mb-1 ${message.role === "assistant" ? "text-blue-600" : "text-green-600"}`}>
                        {message.role === "assistant" ? "AI Interviewer" : "Anda"}
                      </div>
                      <p className="text-gray-800 leading-relaxed">{message.content}</p>
                      {message.role === "assistant" && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-2 h-7 px-3 text-xs hover:bg-blue-100"
                          onClick={() => playMessage(message.content, message.audioUrl)}
                        >
                          <Play className="h-3 w-3 mr-1" /> Putar Ulang
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {messages.length === 0 && !isProcessing && (
                <div className="text-center text-gray-500 py-12">
                  <Bot className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium">
                    {isInitialized ? "Mulai berbicara dengan menekan tombol mikrofon di bawah" : "Memulai wawancara..."}
                  </p>
                  <p className="text-sm mt-2">Percakapan terbaru akan muncul di atas</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Video/Avatar Section (Bawah) */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 flex-1">
          {/* AI Interviewer (Kiri) */}
          <Card className="flex flex-col">
            <CardHeader className="bg-blue-50 text-center py-3">
              <CardTitle className="text-base">AI Interviewer</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col items-center justify-center p-6">
              <Avatar className="w-24 h-24 mb-3 bg-blue-100">
                <AvatarFallback className="text-3xl">
                  <Bot className="h-12 w-12 text-blue-500" />
                </AvatarFallback>
              </Avatar>
              <p className="text-center text-sm text-gray-600">
                {VOICE_OPTIONS[voiceGender].name}
              </p>
              <div className={`w-3 h-3 rounded-full mt-2 ${isProcessing ? "bg-yellow-400 animate-pulse" : "bg-green-400"}`}></div>
            </CardContent>
          </Card>

          {/* User/Interviewee (Kanan) */}
          <Card className="flex flex-col">
            <CardHeader className="bg-green-50 text-center py-3">
              <CardTitle className="text-base">Kandidat</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col items-center justify-center p-6">
              <Avatar className="w-24 h-24 mb-3 bg-green-100">
                <AvatarFallback className="text-3xl">
                  <User className="h-12 w-12 text-green-500" />
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <Button
                  onClick={toggleRecording}
                  disabled={isProcessing}
                  size="lg"
                  className={`rounded-full h-14 w-14 mb-2 shadow-lg transition-all duration-200 ${
                    isRecording
                      ? "bg-red-500 hover:bg-red-600 animate-pulse"
                      : "bg-green-500 hover:bg-green-600 hover:scale-105"
                  }`}
                >
                  {isRecording ? <MicOff className="h-7 w-7" /> : <Mic className="h-7 w-7" />}
                </Button>
                <p className="text-xs text-gray-600">
                  {isProcessing
                    ? "Memproses..."
                    : isRecording
                      ? `Merekam... (${recordingTime}s)`
                      : "Klik untuk berbicara"}
                </p>
                <div className={`w-3 h-3 rounded-full mt-2 mx-auto ${isRecording ? "bg-red-400 animate-pulse" : isProcessing ? "bg-yellow-400 animate-pulse" : "bg-gray-300"}`}></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, User, Bo<PERSON>, Loader2, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { processUserInput, speechToText, textToSpeech, VOICE_OPTIONS, VoiceGender } from "@/lib/ai-service"

type Message = {
  role: "user" | "assistant"
  content: string
  audioUrl?: string
}

export default function InterviewPage() {
  // State untuk menentukan apakah sudah memilih gender atau belum
  const [hasSelectedGender, setHasSelectedGender] = useState(false)
  const [voiceGender, setVoiceGender] = useState<VoiceGender>('female')
  
  // State untuk interview
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [recordingTime, setRecordingTime] = useState(0)

  // Refs untuk audio recording
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  
  // Function untuk memilih gender dan mulai interview
  const handleGenderSelection = (gender: VoiceGender) => {
    setVoiceGender(gender)
    setHasSelectedGender(true)
  }

  // Initialize interview setelah gender dipilih
  useEffect(() => {
    if (hasSelectedGender && !isInitialized) {
      const initializeInterview = async () => {
        setIsProcessing(true)
        try {
          console.log("Initializing interview...")
          
          const initialPrompt = "Perkenalkan diri Anda sebagai AI interviewer dan mulai wawancara kerja dalam bahasa Indonesia."

          console.log("Sending initial prompt to AI...")
          const initialResponse = await processUserInput(initialPrompt)
          console.log("Received initial AI response:", initialResponse)

          // Convert the initial response to speech
          let audioUrl = ""
          try {
            console.log("Converting initial response to speech...")
            audioUrl = await textToSpeech(initialResponse, voiceGender)
          } catch (speechError) {
            console.warn("Text-to-speech failed, continuing without audio:", speechError)
          }

          setMessages([
            {
              role: "assistant",
              content: initialResponse,
              audioUrl: audioUrl,
            },
          ])
          console.log("Interview initialized successfully")
        } catch (error) {
          console.error("Error initializing interview:", error)
          if (error instanceof Error) {
            setError(`Gagal memulai wawancara: ${error.message}. Silakan periksa konfigurasi API key.`)
          } else {
            setError("Gagal memulai wawancara. Silakan muat ulang halaman.")
          }
        } finally {
          setIsProcessing(false)
          setIsInitialized(true)
        }
      }

      initializeInterview()
    }
  }, [hasSelectedGender, isInitialized, voiceGender])

  // Start the recording timer
  const startTimer = () => {
    setRecordingTime(0)
    timerRef.current = setInterval(() => {
      setRecordingTime((prevTime) => prevTime + 1)
    }, 1000)
  }

  // Stop the recording timer
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }

  // Start recording using MediaRecorder API
  const startRecording = async () => {
    setIsRecording(true)
    audioChunksRef.current = []

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      // Create a new MediaRecorder instance
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder

      // Set up event handlers
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      // Start recording
      mediaRecorder.start()
      startTimer()
    } catch (error) {
      console.error("Error accessing microphone:", error)
      setError("Gagal mengakses mikrofon. Pastikan Anda memberikan izin.")
      setIsRecording(false)
    }
  }

  // Stop recording and process the audio
  const stopRecording = async () => {
    setIsRecording(false)
    stopTimer()

    if (!mediaRecorderRef.current) {
      setError("Tidak ada rekaman yang sedang berlangsung.")
      return
    }

    try {
      // Create a Promise that resolves when the MediaRecorder stops
      const recordingData = await new Promise<Blob>((resolve) => {
        mediaRecorderRef.current!.onstop = () => {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' })
          resolve(audioBlob)
        }
        mediaRecorderRef.current!.stop()
      })

      // Stop all tracks in the stream
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop())

      // Process the recording
      await processRecording(recordingData)
    } catch (error) {
      console.error("Error stopping recording:", error)
      setError("Terjadi kesalahan saat menghentikan rekaman. Silakan coba lagi.")
      setIsProcessing(false)
    }
  }

  // Process audio recording
  const processRecording = async (audioData: Blob) => {
    setIsProcessing(true)

    try {
      console.log("Starting to process audio recording...")

      // Convert speech to text using browser API
      console.log("Converting speech to text...")
      const transcript = await speechToText()
      console.log("Received transcript:", transcript)

      if (!transcript || transcript.trim() === "") {
        throw new Error("Tidak ada teks yang terdeteksi dalam rekaman. Silakan coba lagi dengan suara yang lebih jelas.")
      }

      // Add user message
      const userMessage: Message = {
        role: "user",
        content: transcript,
      }

      setMessages((prev) => [...prev, userMessage])

      // Process with AI and get response
      console.log("Sending transcript to AI for processing...")
      const aiResponse = await processUserInput(transcript)
      console.log("Received AI response:", aiResponse)

      // Convert AI response to speech
      console.log("Converting AI response to speech...")
      const audioUrl = await textToSpeech(aiResponse, voiceGender)

      // Add AI message
      const assistantMessage: Message = {
        role: "assistant",
        content: aiResponse,
        audioUrl: audioUrl,
      }

      setMessages((prev) => [...prev, assistantMessage])
      console.log("Processing complete")
    } catch (error) {
      console.error("Error processing recording:", error)
      if (error instanceof Error) {
        setError(`Terjadi kesalahan: ${error.message}`)
      } else {
        setError("Terjadi kesalahan saat memproses rekaman. Silakan coba lagi.")
      }
    } finally {
      setIsProcessing(false)
    }
  }

  // Toggle recording state
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording()
    } else {
      startRecording()
    }
  }

  // Function to play message text using text-to-speech
  const playMessage = async (text: string, audioUrl?: string) => {
    try {
      if (audioUrl) {
        // If we have a stored audio URL, play it directly
        const audio = new Audio(audioUrl)
        audio.play()
      } else {
        // Otherwise, generate new speech with current voice gender
        await textToSpeech(text, voiceGender)
      }
    } catch (error) {
      console.error("Error playing message:", error)
      setError("Gagal memutar pesan. Silakan coba lagi.")
    }
  }

  // Function to export the transcript
  const exportTranscript = () => {
    try {
      // Format the transcript
      const date = new Date().toLocaleString("id-ID")
      let transcriptContent = `AI Interview Transcript - ${date}\n\n`

      messages.forEach((message) => {
        const role = message.role === "assistant" ? "AI Interviewer" : "Kandidat"
        transcriptContent += `${role}: ${message.content}\n\n`
      })

      // Create a blob and download link
      const blob = new Blob([transcriptContent], { type: "text/plain;charset=utf-8" })
      const url = URL.createObjectURL(blob)

      // Create a temporary link and trigger download
      const link = document.createElement("a")
      link.href = url
      link.download = `interview-transcript-${new Date().toISOString().slice(0, 10)}.txt`
      document.body.appendChild(link)
      link.click()

      // Clean up
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error exporting transcript:", error)
      setError("Gagal mengekspor transkrip. Silakan coba lagi.")
    }
  }

  // Jika belum memilih gender, tampilkan halaman pilihan
  if (!hasSelectedGender) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-center">
            <CardTitle className="text-3xl font-bold">AI Interview System</CardTitle>
            <p className="text-blue-100 mt-2">Pilih suara AI interviewer yang Anda inginkan</p>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-4">Pilih Gender Suara AI Interviewer:</h2>
                <p className="text-gray-600 mb-8">
                  Pilih suara yang membuat Anda merasa nyaman selama wawancara
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card
                  className="cursor-pointer hover:shadow-lg transition-all duration-300 border-2 hover:border-pink-300"
                  onClick={() => handleGenderSelection('female')}
                >
                  <CardContent className="p-6 text-center">
                    <div className="text-6xl mb-4">👩‍💼</div>
                    <h3 className="text-xl font-semibold mb-2">{VOICE_OPTIONS.female.name}</h3>
                    <p className="text-gray-600 text-sm">{VOICE_OPTIONS.female.description}</p>
                    <Button className="mt-4 w-full bg-pink-500 hover:bg-pink-600">
                      Pilih Suara Perempuan
                    </Button>
                  </CardContent>
                </Card>

                <Card
                  className="cursor-pointer hover:shadow-lg transition-all duration-300 border-2 hover:border-blue-300"
                  onClick={() => handleGenderSelection('male')}
                >
                  <CardContent className="p-6 text-center">
                    <div className="text-6xl mb-4">👨‍💼</div>
                    <h3 className="text-xl font-semibold mb-2">{VOICE_OPTIONS.male.name}</h3>
                    <p className="text-gray-600 text-sm">{VOICE_OPTIONS.male.description}</p>
                    <Button className="mt-4 w-full bg-blue-500 hover:bg-blue-600">
                      Pilih Suara Laki-laki
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Interface wawancara dengan layout kiri-kanan
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">AI Interview System</h1>
            <p className="text-blue-100">Suara: {VOICE_OPTIONS[voiceGender].name}</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportTranscript}
              disabled={messages.length === 0}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            >
              <Download className="h-4 w-4 mr-1" /> Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setHasSelectedGender(false)}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            >
              Ganti Suara
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-4 h-[calc(100vh-80px)] flex flex-col">
        {/* Video/Avatar Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 flex-1 mb-4">
          {/* AI Interviewer (Kiri) */}
          <Card className="flex flex-col">
            <CardHeader className="bg-blue-50 text-center">
              <CardTitle className="text-lg">AI Interviewer</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col items-center justify-center p-8">
              <Avatar className="w-32 h-32 mb-4 bg-blue-100">
                <AvatarFallback className="text-4xl">
                  <Bot className="h-16 w-16 text-blue-500" />
                </AvatarFallback>
              </Avatar>
              <p className="text-center text-gray-600">
                {isProcessing ? "Sedang berpikir..." : "Siap untuk wawancara"}
              </p>
              {isProcessing && <Loader2 className="h-6 w-6 animate-spin mt-2 text-blue-500" />}
            </CardContent>
          </Card>

          {/* User/Interviewee (Kanan) */}
          <Card className="flex flex-col">
            <CardHeader className="bg-green-50 text-center">
              <CardTitle className="text-lg">Kandidat</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col items-center justify-center p-8">
              <Avatar className="w-32 h-32 mb-4 bg-green-100">
                <AvatarFallback className="text-4xl">
                  <User className="h-16 w-16 text-green-500" />
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <Button
                  onClick={toggleRecording}
                  disabled={isProcessing}
                  size="lg"
                  className={`rounded-full h-16 w-16 mb-2 ${
                    isRecording ? "bg-red-500 hover:bg-red-600" : "bg-green-500 hover:bg-green-600"
                  }`}
                >
                  {isRecording ? <MicOff className="h-8 w-8" /> : <Mic className="h-8 w-8" />}
                </Button>
                <p className="text-sm text-gray-600">
                  {isProcessing
                    ? "Memproses..."
                    : isRecording
                      ? `Merekam... (${recordingTime}s)`
                      : "Klik untuk berbicara"}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Transcript Section (Bawah) */}
        <Card className="h-64">
          <CardHeader className="bg-gray-50 py-3">
            <CardTitle className="text-lg">Transkrip Percakapan</CardTitle>
          </CardHeader>
          <CardContent className="p-4 h-52 overflow-y-auto">
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <p>{error}</p>
                <Button variant="outline" size="sm" className="mt-2" onClick={() => setError(null)}>
                  Tutup
                </Button>
              </div>
            )}

            <div className="space-y-4">
              {messages.map((message, index) => (
                <div key={index} className={`flex ${message.role === "assistant" ? "justify-start" : "justify-end"}`}>
                  <div
                    className={`flex gap-3 max-w-[80%] ${message.role === "assistant" ? "flex-row" : "flex-row-reverse"}`}
                  >
                    <Avatar className={`w-8 h-8 ${message.role === "assistant" ? "bg-blue-100" : "bg-green-100"}`}>
                      <AvatarFallback>
                        {message.role === "assistant" ? (
                          <Bot className="h-4 w-4 text-blue-500" />
                        ) : (
                          <User className="h-4 w-4 text-green-500" />
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`rounded-lg p-3 text-sm ${message.role === "assistant" ? "bg-blue-100" : "bg-green-100"}`}>
                      <p>{message.content}</p>
                      {message.role === "assistant" && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-1 h-6 px-2 text-xs"
                          onClick={() => playMessage(message.content, message.audioUrl)}
                        >
                          <Play className="h-3 w-3 mr-1" /> Play
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {messages.length === 0 && !isProcessing && (
                <div className="text-center text-gray-500 py-8">
                  {isInitialized ? "Mulai berbicara dengan menekan tombol mikrofon" : "Memulai wawancara..."}
                </div>
              )}

              {isProcessing && (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { processUserInput } from "@/lib/ai-service"

export default function DebugPage() {
  const [testResult, setTestResult] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)

  const testApiConnection = async () => {
    setIsLoading(true)
    setTestResult("Testing...")
    
    try {
      const response = await processUserInput("Halo, test koneksi API")
      setTestResult(`✅ API Test Berhasil: ${response}`)
    } catch (error) {
      setTestResult(`❌ API Test Gagal: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto max-w-4xl py-8">
      <Card className="w-full">
        <CardHeader className="bg-gradient-to-r from-green-600 to-teal-600 text-white">
          <CardTitle className="text-2xl font-bold text-center">Debug Page - API Test</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-2">Environment Variables Status:</h3>
              <p>NEXT_PUBLIC_GROQ_API_KEY: {process.env.NEXT_PUBLIC_GROQ_API_KEY ? '✅ Set' : '❌ Not Set'}</p>
            </div>
            
            <Button 
              onClick={testApiConnection} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? "Testing..." : "Test API Connection"}
            </Button>
            
            {testResult && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold mb-2">Test Result:</h3>
                <p className="whitespace-pre-wrap">{testResult}</p>
              </div>
            )}
            
            <div className="mt-4">
              <Button 
                onClick={() => window.location.href = '/'}
                variant="outline"
              >
                Back to Main App
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

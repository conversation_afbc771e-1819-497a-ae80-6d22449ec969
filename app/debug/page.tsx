"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { processUserInput, textToSpeech, VOICE_OPTIONS, VoiceGender } from "@/lib/ai-service"

export default function DebugPage() {
  const [testResult, setTestResult] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)

  const testApiConnection = async () => {
    setIsLoading(true)
    setTestResult("Testing Gemini AI...")

    try {
      const response = await processUserInput("Halo, test koneksi API")
      setTestResult(`✅ Gemini AI Test Berhasil: ${response}`)
    } catch (error) {
      setTestResult(`❌ Gemini AI Test Gagal: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testTTSConnection = async (gender: VoiceGender) => {
    setIsLoading(true)
    setTestResult(`Testing ElevenLabs TTS with ${gender} voice...`)

    try {
      await textToSpeech(`Halo, ini adalah test text to speech menggunakan ElevenLabs dengan suara ${gender === 'female' ? 'perempuan' : 'laki-laki'}`, gender)
      setTestResult(`✅ ElevenLabs TTS Test Berhasil (${VOICE_OPTIONS[gender].name}): Audio berhasil diputar`)
    } catch (error) {
      setTestResult(`❌ ElevenLabs TTS Test Gagal (${VOICE_OPTIONS[gender].name}): ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto max-w-4xl py-8">
      <Card className="w-full">
        <CardHeader className="bg-gradient-to-r from-green-600 to-teal-600 text-white">
          <CardTitle className="text-2xl font-bold text-center">Debug Page - API Test</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-2">Environment Variables Status:</h3>
              <p>NEXT_PUBLIC_GEMINI_API_KEY: {process.env.NEXT_PUBLIC_GEMINI_API_KEY ? '✅ Set (Text Generation)' : '❌ Not Set'}</p>
              <p>NEXT_PUBLIC_ELEVENLABS_API_KEY: {process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY ? '✅ Set (Text-to-Speech)' : '❌ Not Set'}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={testApiConnection}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? "Testing..." : "Test Gemini AI"}
              </Button>

              <Button
                onClick={() => testTTSConnection('female')}
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? "Testing..." : "Test TTS Perempuan"}
              </Button>

              <Button
                onClick={() => testTTSConnection('male')}
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? "Testing..." : "Test TTS Laki-laki"}
              </Button>
            </div>

            {testResult && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold mb-2">Test Result:</h3>
                <p className="whitespace-pre-wrap">{testResult}</p>
              </div>
            )}

            <div className="mt-4">
              <Button
                onClick={() => window.location.href = '/'}
                variant="outline"
              >
                Back to Main App
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

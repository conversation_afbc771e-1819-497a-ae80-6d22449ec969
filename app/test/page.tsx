"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestPage() {
  return (
    <div className="container mx-auto max-w-4xl py-8">
      <Card className="w-full">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <CardTitle className="text-2xl font-bold text-center">Test Page - AI Interview System</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <h2 className="text-xl font-semibold">Aplikasi Berhasil Dimuat!</h2>
            <p className="text-gray-600">
              Jika Anda melihat halaman ini, berarti Next.js dan komponen UI sudah berfungsi dengan baik.
            </p>
            <Button className="bg-blue-500 hover:bg-blue-600">
              Test Button
            </Button>
            <div className="mt-4 p-4 bg-green-100 rounded-lg">
              <p className="text-green-800">✅ React Components: Working</p>
              <p className="text-green-800">✅ Tailwind CSS: Working</p>
              <p className="text-green-800">✅ UI Components: Working</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

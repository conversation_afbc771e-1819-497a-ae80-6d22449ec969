"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { textToSpeech, VOICE_OPTIONS, VoiceGender } from "@/lib/ai-service"

export default function VoiceTestPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string>("")

  const testVoice = async (gender: VoiceGender) => {
    setIsLoading(true)
    setTestResult(`Testing ${gender} voice...`)
    
    try {
      const testText = `<PERSON><PERSON>, saya adalah AI interviewer dengan suara ${gender === 'female' ? 'perempuan' : 'laki-laki'}. Ini adalah test untuk memastikan suara yang tepat keluar.`
      
      console.log(`Testing voice for gender: ${gender}`)
      console.log(`Voice ID: ${VOICE_OPTIONS[gender].id}`)
      console.log(`Voice Name: ${VOICE_OPTIONS[gender].name}`)
      
      await textToSpeech(testText, gender)
      setTestResult(`✅ ${VOICE_OPTIONS[gender].name} voice test completed successfully!`)
    } catch (error) {
      console.error("Voice test error:", error)
      setTestResult(`❌ Voice test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto max-w-4xl py-8">
      <Card className="w-full">
        <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
          <CardTitle className="text-2xl font-bold text-center">Voice Test Page</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-4">Test Voice Gender Selection</h2>
              <p className="text-gray-600 mb-6">
                Klik tombol di bawah untuk test masing-masing suara
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-2 border-pink-200">
                <CardContent className="p-6 text-center">
                  <div className="text-4xl mb-4">👩‍💼</div>
                  <h3 className="text-lg font-semibold mb-2">{VOICE_OPTIONS.female.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{VOICE_OPTIONS.female.description}</p>
                  <p className="text-xs text-gray-500 mb-4">Voice: id-ID-Standard-A</p>
                  <Button
                    onClick={() => testVoice('female')}
                    disabled={isLoading}
                    className="w-full bg-pink-500 hover:bg-pink-600"
                  >
                    {isLoading ? "Testing..." : "Test Female Voice"}
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-2 border-blue-200">
                <CardContent className="p-6 text-center">
                  <div className="text-4xl mb-4">👨‍💼</div>
                  <h3 className="text-lg font-semibold mb-2">{VOICE_OPTIONS.male.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{VOICE_OPTIONS.male.description}</p>
                  <p className="text-xs text-gray-500 mb-4">Voice: id-ID-Standard-C</p>
                  <Button
                    onClick={() => testVoice('male')}
                    disabled={isLoading}
                    className="w-full bg-blue-500 hover:bg-blue-600"
                  >
                    {isLoading ? "Testing..." : "Test Male Voice"}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {testResult && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold mb-2">Test Result:</h3>
                <p className="whitespace-pre-wrap">{testResult}</p>
              </div>
            )}

            <div className="text-center">
              <Button 
                onClick={() => window.location.href = '/'}
                variant="outline"
              >
                Back to Main App
              </Button>
            </div>

            <div className="p-4 bg-yellow-50 rounded-lg">
              <h3 className="font-semibold mb-2">Debug Info:</h3>
              <p className="text-sm">Buka browser console (F12) untuk melihat log detail saat testing voice.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

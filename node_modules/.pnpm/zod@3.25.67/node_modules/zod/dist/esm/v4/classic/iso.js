import * as core from "zod/v4/core";
import * as schemas from "./schemas.js";
export const ZodISODateTime = /*@__PURE__*/ core.$constructor("ZodISODateTime", (inst, def) => {
    core.$ZodISODateTime.init(inst, def);
    schemas.ZodStringFormat.init(inst, def);
});
export function datetime(params) {
    return core._isoDateTime(ZodISODateTime, params);
}
export const ZodISODate = /*@__PURE__*/ core.$constructor("ZodISODate", (inst, def) => {
    core.$ZodISODate.init(inst, def);
    schemas.ZodStringFormat.init(inst, def);
});
export function date(params) {
    return core._isoDate(ZodISODate, params);
}
export const ZodISOTime = /*@__PURE__*/ core.$constructor("ZodISOTime", (inst, def) => {
    core.$ZodISOTime.init(inst, def);
    schemas.ZodStringFormat.init(inst, def);
});
export function time(params) {
    return core._isoTime(ZodISOTime, params);
}
export const ZodISODuration = /*@__PURE__*/ core.$constructor("ZodISODuration", (inst, def) => {
    core.$ZodISODuration.init(inst, def);
    schemas.ZodStringFormat.init(inst, def);
});
export function duration(params) {
    return core._isoDuration(ZodISODuration, params);
}

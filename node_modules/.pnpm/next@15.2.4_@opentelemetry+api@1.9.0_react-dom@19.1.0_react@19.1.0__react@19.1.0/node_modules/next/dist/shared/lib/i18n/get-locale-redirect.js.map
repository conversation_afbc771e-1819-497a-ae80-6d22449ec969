{"version": 3, "sources": ["../../../../src/shared/lib/i18n/get-locale-redirect.ts"], "sourcesContent": ["import type { DomainLocale } from '../../../server/config'\nimport type { I18NConfig } from '../../../server/config-shared'\nimport { acceptLanguage } from '../../../server/accept-header'\nimport { denormalizePagePath } from '../page-path/denormalize-page-path'\nimport { detectDomainLocale } from './detect-domain-locale'\nimport { formatUrl } from '../router/utils/format-url'\nimport { getCookieParser } from '../../../server/api-utils/get-cookie-parser'\n\ninterface Options {\n  defaultLocale: string\n  domainLocale?: DomainLocale\n  headers?: { [key: string]: string | string[] | undefined }\n  nextConfig: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  pathLocale?: string\n  urlParsed: { hostname?: string | null; pathname: string }\n}\n\nfunction getLocaleFromCookie(\n  i18n: I18NConfig,\n  headers: { [key: string]: string | string[] | undefined } = {}\n) {\n  const nextLocale = getCookieParser(\n    headers || {}\n  )()?.NEXT_LOCALE?.toLowerCase()\n  return nextLocale\n    ? i18n.locales.find((locale) => nextLocale === locale.toLowerCase())\n    : undefined\n}\n\nfunction detectLocale({\n  i18n,\n  headers,\n  domainLocale,\n  preferredLocale,\n  pathLocale,\n}: {\n  i18n: I18NConfig\n  preferredLocale?: string\n  headers?: { [key: string]: string | string[] | undefined }\n  domainLocale?: DomainLocale\n  pathLocale?: string\n}) {\n  return (\n    pathLocale ||\n    domainLocale?.defaultLocale ||\n    getLocaleFromCookie(i18n, headers) ||\n    preferredLocale ||\n    i18n.defaultLocale\n  )\n}\n\nfunction getAcceptPreferredLocale(\n  i18n: I18NConfig,\n  headers?: { [key: string]: string | string[] | undefined }\n) {\n  if (\n    headers?.['accept-language'] &&\n    !Array.isArray(headers['accept-language'])\n  ) {\n    try {\n      return acceptLanguage(headers['accept-language'], i18n.locales)\n    } catch (err) {}\n  }\n}\n\nexport function getLocaleRedirect({\n  defaultLocale,\n  domainLocale,\n  pathLocale,\n  headers,\n  nextConfig,\n  urlParsed,\n}: Options) {\n  if (\n    nextConfig.i18n &&\n    nextConfig.i18n.localeDetection !== false &&\n    denormalizePagePath(urlParsed.pathname) === '/'\n  ) {\n    const preferredLocale = getAcceptPreferredLocale(nextConfig.i18n, headers)\n    const detectedLocale = detectLocale({\n      i18n: nextConfig.i18n,\n      preferredLocale,\n      headers,\n      pathLocale,\n      domainLocale,\n    })\n\n    const preferredDomain = detectDomainLocale(\n      nextConfig.i18n.domains,\n      undefined,\n      preferredLocale\n    )\n\n    if (domainLocale && preferredDomain) {\n      const isPDomain = preferredDomain.domain === domainLocale.domain\n      const isPLocale = preferredDomain.defaultLocale === preferredLocale\n      if (!isPDomain || !isPLocale) {\n        const scheme = `http${preferredDomain.http ? '' : 's'}`\n        const rlocale = isPLocale ? '' : preferredLocale\n        return `${scheme}://${preferredDomain.domain}/${rlocale}`\n      }\n    }\n\n    if (detectedLocale.toLowerCase() !== defaultLocale.toLowerCase()) {\n      return formatUrl({\n        ...urlParsed,\n        pathname: `${nextConfig.basePath || ''}/${detectedLocale}${\n          nextConfig.trailingSlash ? '/' : ''\n        }`,\n      })\n    }\n  }\n}\n"], "names": ["getLocaleRedirect", "getLocaleFromCookie", "i18n", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextLocale", "NEXT_LOCALE", "toLowerCase", "locales", "find", "locale", "undefined", "detectLocale", "domainLocale", "preferredLocale", "pathLocale", "defaultLocale", "getAcceptPreferredLocale", "Array", "isArray", "acceptLanguage", "err", "nextConfig", "urlParsed", "localeDetection", "denormalizePagePath", "pathname", "detectedLocale", "preferredDomain", "detectDomainLocale", "domains", "isPDomain", "domain", "isPLocale", "scheme", "http", "rlocale", "formatUrl", "basePath", "trailingSlash"], "mappings": ";;;;+BAqEgBA;;;eAAAA;;;8BAnEe;qCACK;oCACD;2BACT;iCACM;AAehC,SAASC,oBACPC,IAAgB,EAChBC,OAA8D;IAA9DA,IAAAA,oBAAAA,UAA4D,CAAC;QAE1CC,8BAAAA;IAAnB,MAAMC,cAAaD,mBAAAA,IAAAA,gCAAe,EAChCD,WAAW,CAAC,0BADKC,+BAAAA,iBAEdE,WAAW,qBAFGF,6BAEDG,WAAW;IAC7B,OAAOF,aACHH,KAAKM,OAAO,CAACC,IAAI,CAAC,CAACC,SAAWL,eAAeK,OAAOH,WAAW,MAC/DI;AACN;AAEA,SAASC,aAAa,KAYrB;IAZqB,IAAA,EACpBV,IAAI,EACJC,OAAO,EACPU,YAAY,EACZC,eAAe,EACfC,UAAU,EAOX,GAZqB;IAapB,OACEA,eACAF,gCAAAA,aAAcG,aAAa,KAC3Bf,oBAAoBC,MAAMC,YAC1BW,mBACAZ,KAAKc,aAAa;AAEtB;AAEA,SAASC,yBACPf,IAAgB,EAChBC,OAA0D;IAE1D,IACEA,CAAAA,2BAAAA,OAAS,CAAC,kBAAkB,KAC5B,CAACe,MAAMC,OAAO,CAAChB,OAAO,CAAC,kBAAkB,GACzC;QACA,IAAI;YACF,OAAOiB,IAAAA,4BAAc,EAACjB,OAAO,CAAC,kBAAkB,EAAED,KAAKM,OAAO;QAChE,EAAE,OAAOa,KAAK,CAAC;IACjB;AACF;AAEO,SAASrB,kBAAkB,KAOxB;IAPwB,IAAA,EAChCgB,aAAa,EACbH,YAAY,EACZE,UAAU,EACVZ,OAAO,EACPmB,UAAU,EACVC,SAAS,EACD,GAPwB;IAQhC,IACED,WAAWpB,IAAI,IACfoB,WAAWpB,IAAI,CAACsB,eAAe,KAAK,SACpCC,IAAAA,wCAAmB,EAACF,UAAUG,QAAQ,MAAM,KAC5C;QACA,MAAMZ,kBAAkBG,yBAAyBK,WAAWpB,IAAI,EAAEC;QAClE,MAAMwB,iBAAiBf,aAAa;YAClCV,MAAMoB,WAAWpB,IAAI;YACrBY;YACAX;YACAY;YACAF;QACF;QAEA,MAAMe,kBAAkBC,IAAAA,sCAAkB,EACxCP,WAAWpB,IAAI,CAAC4B,OAAO,EACvBnB,WACAG;QAGF,IAAID,gBAAgBe,iBAAiB;YACnC,MAAMG,YAAYH,gBAAgBI,MAAM,KAAKnB,aAAamB,MAAM;YAChE,MAAMC,YAAYL,gBAAgBZ,aAAa,KAAKF;YACpD,IAAI,CAACiB,aAAa,CAACE,WAAW;gBAC5B,MAAMC,SAAS,AAAC,SAAMN,CAAAA,gBAAgBO,IAAI,GAAG,KAAK,GAAE;gBACpD,MAAMC,UAAUH,YAAY,KAAKnB;gBACjC,OAAO,AAAGoB,SAAO,QAAKN,gBAAgBI,MAAM,GAAC,MAAGI;YAClD;QACF;QAEA,IAAIT,eAAepB,WAAW,OAAOS,cAAcT,WAAW,IAAI;YAChE,OAAO8B,IAAAA,oBAAS,EAAC;gBACf,GAAGd,SAAS;gBACZG,UAAU,AAAGJ,CAAAA,WAAWgB,QAAQ,IAAI,EAAC,IAAE,MAAGX,iBACxCL,CAAAA,WAAWiB,aAAa,GAAG,MAAM,EAAC;YAEtC;QACF;IACF;AACF"}
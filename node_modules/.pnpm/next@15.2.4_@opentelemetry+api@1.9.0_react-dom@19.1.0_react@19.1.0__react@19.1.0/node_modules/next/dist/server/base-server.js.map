{"version": 3, "sources": ["../../src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport {\n  type FallbackRouteParams,\n  getFallbackRouteParams,\n} from './request/fallback-params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport {\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ServerComponentsHmrCache,\n  type ResponseCacheBase,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n  CachedRouteKind,\n  type CachedRedirectValue,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PreviewData } from '../types'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from './route-modules/app-page/module'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { AppRouteRouteHandlerContext } from './route-modules/app-route/module'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport { getRedirectStatus } from '../lib/redirect-status'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { getCacheControlHeader, type CacheControl } from './lib/cache-control'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { getBotType, isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport { getTracer, isBubbledError, SpanKind } from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  normalizeNextQueryParam,\n  toNodeOutgoingHttpHeaders,\n} from './web/utils'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  MATCHED_PATH_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from './web/spec-extension/adapters/next-request'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { decodePathParams } from './lib/router-utils/decode-path-params'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n  isPagesRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { ENCODED_TAGS } from './stream-utils/encodedTags'\nimport { NextRequestHint } from './web/adapter'\nimport { getRevalidateReason } from './instrumentation/utils'\nimport { RouteKind } from './route-kind'\nimport type { RouteModule } from './route-modules/route-module'\nimport { FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { toResponseCacheEntry } from './response-cache/utils'\nimport { scheduleOnNextTick } from '../lib/scheduler'\nimport { SegmentPrefixRSCPathnameNormalizer } from './normalizers/request/segment-prefix-rsc'\nimport {\n  shouldServeStreamingMetadata,\n  isHtmlBotRequest,\n} from './lib/streaming-metadata'\nimport { getCacheHandlers } from './use-cache/handlers'\nimport { InvariantError } from '../shared/lib/invariant-error'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\nexport class NoFallbackError extends Error {}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  type: 'html' | 'json' | 'rsc'\n  body: RenderResult\n  cacheControl?: CacheControl\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n    requestProtocol: 'http' | 'https'\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly segmentPrefetchRSC: SegmentPrefixRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n  private readonly isAppSegmentPrefetchEnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for dynamic IO\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir =\n      process.env.NEXT_RUNTIME === 'edge' ? dir : require('path').resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? this.nextConfig.distDir\n        : require('path').join(this.dir, this.nextConfig.distDir)\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.isAppSegmentPrefetchEnabled =\n      this.enabledDirectories.app &&\n      this.nextConfig.experimental.clientSegmentCache === true\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      segmentPrefetchRSC:\n        this.isAppSegmentPrefetchEnabled && this.minimalMode\n          ? new SegmentPrefixRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n    }\n\n    this.renderOpts = {\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      strictNextHead: this.nextConfig.experimental.strictNextHead ?? true,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      // @ts-expect-error internal field not publicly exposed\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      htmlLimitedBots: this.nextConfig.htmlLimitedBots,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        dynamicIO: this.nextConfig.experimental.dynamicIO ?? false,\n        clientSegmentCache:\n          this.nextConfig.experimental.clientSegmentCache ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  protected reloadMatchers() {\n    return this.matchers.reload()\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.segmentPrefetchRSC?.match(parsedUrl.pathname)) {\n      const result = this.normalizers.segmentPrefetchRSC.extract(\n        parsedUrl.pathname\n      )\n      if (!result) return false\n\n      const { originalPathname, segmentPath } = result\n      parsedUrl.pathname = originalPathname\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n        segmentPath\n\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPath)\n    } else if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n\n        const segmentPrefetchRSCRequest =\n          req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()]\n        if (typeof segmentPrefetchRSCRequest === 'string') {\n          addRequestMeta(\n            req,\n            'segmentPrefetchRSCRequest',\n            segmentPrefetchRSCRequest\n          )\n        }\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = await this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          getRequestMeta(req, 'middlewareInvoke')\n        ) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        addRequestMeta(req, 'locale', localePathResult.detectedLocale)\n        addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          removeRequestMeta(req, 'localeInferredFromDefault')\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      addRequestMeta(req, 'isNextDataReq', true)\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            req.headers[MATCHED_PATH_HEADER] as string,\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            addRequestMeta(req, 'locale', localeAnalysisResult.detectedLocale)\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              addRequestMeta(req, 'localeInferredFromDefault', true)\n            } else {\n              removeRequestMeta(req, 'localeInferredFromDefault')\n            }\n          }\n\n          // TODO: check if this is needed any more?\n          matchedPath = denormalizePagePath(matchedPath)\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n              // The page is dynamic if the params are defined.\n              pageIsDynamic = typeof match.params !== 'undefined'\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParamKeys = Object.keys(\n            utils.handleRewrites(req, parsedUrl)\n          )\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n\n          // Create a copy of the query params to avoid mutating the original\n          // object. This prevents any overlapping query params that have the\n          // same normalized key from causing issues.\n          const queryParams = { ...parsedUrl.query }\n\n          for (const [key, value] of Object.entries(parsedUrl.query)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            // Remove the prefixed key from the query params because we want\n            // to consume it for the dynamic route matcher.\n            delete parsedUrl.query[key]\n\n            if (typeof value === 'undefined') continue\n            queryParams[normalizedKey] = value\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            let paramsResult = utils.normalizeDynamicRouteParams(\n              queryParams,\n              false\n            )\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams, false)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult = utils.normalizeDynamicRouteParams(\n                  matcherParams,\n                  false\n                )\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            const routeMatchesHeader = req.headers['x-now-route-matches']\n            if (\n              typeof routeMatchesHeader === 'string' &&\n              routeMatchesHeader &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const routeMatches =\n                utils.getParamsFromRouteMatches(routeMatchesHeader)\n\n              if (routeMatches) {\n                paramsResult = utils.normalizeDynamicRouteParams(\n                  routeMatches,\n                  true\n                )\n\n                if (paramsResult.hasValidParams) {\n                  params = paramsResult.params\n                }\n              }\n            }\n\n            // Try to parse the params from the query if we couldn't parse them\n            // from the route matches but ignore missing optional params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // handle the actual dynamic route name being requested\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams &&\n              !utils.normalizeDynamicRouteParams({ ...params }, true)\n                .hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // Mark that the default route matches were set on the request\n              // during routing.\n              addRequestMeta(req, 'didSetDefaultRouteMatches', true)\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n\n              // If the request is for a segment prefetch, we need to update the\n              // segment prefetch request path to include the interpolated\n              // params.\n              let segmentPrefetchRSCRequest = getRequestMeta(\n                req,\n                'segmentPrefetchRSCRequest'\n              )\n              if (\n                segmentPrefetchRSCRequest &&\n                isDynamicRoute(segmentPrefetchRSCRequest, false)\n              ) {\n                segmentPrefetchRSCRequest = utils.interpolateDynamicPath(\n                  segmentPrefetchRSCRequest,\n                  params\n                )\n\n                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n                  segmentPrefetchRSCRequest\n                addRequestMeta(\n                  req,\n                  'segmentPrefetchRSCRequest',\n                  segmentPrefetchRSCRequest\n                )\n              }\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeVercelUrl(req, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !getRequestMeta(req, 'locale')) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          addRequestMeta(req, 'locale', pathnameInfo.locale)\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          addRequestMeta(req, 'localeInferredFromDefault', true)\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        let protocol: 'http:' | 'https:' = 'https:'\n\n        try {\n          const parsedFullUrl = new URL(\n            getRequestMeta(req, 'initURL') || '/',\n            'http://n'\n          )\n          protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n        } catch {}\n\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n          requestProtocol: protocol.substring(0, protocol.length - 1) as\n            | 'http'\n            | 'https',\n        })\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      // If the header is present, receive the expired tags from all the\n      // cache handlers.\n      const handlers = getCacheHandlers()\n      if (handlers) {\n        const header = req.headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER]\n        const expiredTags = typeof header === 'string' ? header.split(',') : []\n\n        const promises: Promise<void>[] = []\n        for (const handler of handlers) {\n          promises.push(handler.receiveExpiredTags(...expiredTags))\n        }\n\n        // Only await if there are any promises to wait for.\n        if (promises.length > 0) await Promise.all(promises)\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath =\n        !useMatchedPathHeader &&\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          addRequestMeta(req, 'locale', invokePathnameInfo.locale)\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales\n        )\n\n        if (normalizeResult.detectedLocale) {\n          addRequestMeta(req, 'locale', normalizeResult.detectedLocale)\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          delete parsedUrl.query[key]\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        getRequestMeta(req, 'middlewareInvoke')\n      ) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the segment prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.segmentPrefetchRSC) {\n      normalizers.push(this.normalizers.segmentPrefetchRSC)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.renderOpts.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    if (this.preparedPromise === null) {\n      // Get instrumentation module\n      this.instrumentation = await this.loadInstrumentationModule()\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const ua = partialContext.req.headers['user-agent'] || ''\n    const isBotRequest = isBot(ua)\n\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: !isBotRequest,\n        botType: getBotType(ua),\n        serveStreamingMetadata: shouldServeStreamingMetadata(\n          ua,\n          this.nextConfig.htmlLimitedBots\n        ),\n      },\n    }\n\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body, type } = payload\n    let { cacheControl } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        cacheControl = undefined\n      }\n\n      if (cacheControl && cacheControl.expire === undefined) {\n        cacheControl.expire = this.nextConfig.expireTime\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        type,\n        generateEtags,\n        poweredByHeader,\n        cacheControl,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.serverOptions.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !getRequestMeta(req, 'isNextDataReq') &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.appendHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.appendHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    let hasGetStaticPaths = !!components.getStaticPaths\n    const isServerAction = getIsServerAction(req)\n    const hasGetInitialProps = !!components.Component?.getInitialProps\n    let isSSG = !!components.getStaticProps\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let fallbackMode: FallbackMode | undefined\n    let hasFallback = false\n\n    const isDynamic = isDynamicRoute(components.page)\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (isAppPath && isDynamic) {\n      const pathsResult = await this.getStaticPaths({\n        pathname,\n        page: components.page,\n        isAppPath,\n        requestHeaders: req.headers,\n      })\n\n      staticPaths = pathsResult.staticPaths\n      fallbackMode = pathsResult.fallbackMode\n      hasFallback = typeof fallbackMode !== 'undefined'\n\n      if (this.nextConfig.output === 'export') {\n        const page = components.page\n        if (!staticPaths) {\n          throw new Error(\n            `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n\n        const resolvedWithoutSlash = removeTrailingSlash(resolvedUrlPathname)\n        if (!staticPaths.includes(resolvedWithoutSlash)) {\n          throw new Error(\n            `Page \"${page}\" is missing param \"${resolvedWithoutSlash}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n      }\n\n      if (hasFallback) {\n        hasGetStaticPaths = true\n      }\n    }\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        getRequestMeta(req, 'isNextDataReq') ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    /**\n     * If true, this indicates that the request being made is for an app\n     * prefetch request.\n     */\n    const isPrefetchRSCRequest =\n      getRequestMeta(req, 'isPrefetchRSCRequest') ?? false\n\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    const locale = getRequestMeta(req, 'locale')\n    const defaultLocale = isSSG\n      ? this.nextConfig.i18n?.defaultLocale\n      : getRequestMeta(req, 'defaultLocale')\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery =\n      hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    const isDebugStaticShell: boolean =\n      hasDebugStaticShellQuery && isRoutePPREnabled\n\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses =\n      isDebugStaticShell && this.renderOpts.dev === true\n\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest =\n      isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = getRequestMeta(\n      req,\n      'segmentPrefetchRSCRequest'\n    )\n\n    const isHtmlBot = isHtmlBotRequest(req)\n    if (isHtmlBot && isRoutePPREnabled) {\n      isSSG = false\n      this.renderOpts.serveStreamingMetadata = false\n    }\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      res.body('Method Not Allowed').send()\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        type: 'html',\n        // TODO: Static pages should be serialized as RenderResult\n        body: RenderResult.fromStatic(components.Component),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const ua = req.headers['user-agent'] || ''\n      const isBotRequest = isBot(ua)\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    const locales = this.nextConfig.i18n?.locales\n\n    let previewData: PreviewData\n    let isPreviewMode = false\n\n    if (hasServerProps || isSSG || isAppPath) {\n      // For the edge runtime, we don't support preview mode in SSG.\n      if (process.env.NEXT_RUNTIME !== 'edge') {\n        const { tryGetPreviewData } =\n          require('./api-utils/node/try-get-preview-data') as typeof import('./api-utils/node/try-get-preview-data')\n        previewData = tryGetPreviewData(\n          req,\n          res,\n          this.renderOpts.previewProps,\n          !!this.nextConfig.experimental.multiZoneDraftMode\n        )\n        isPreviewMode = previewData !== false\n      }\n    }\n\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (\n      isAppPath &&\n      !opts.dev &&\n      !isPreviewMode &&\n      isSSG &&\n      isRSCRequest &&\n      !isDynamicRSCRequest &&\n      (!isEdgeRuntime(opts.runtime) ||\n        (this.serverOptions as any).webServerConfig)\n    ) {\n      stripFlightHeaders(req.headers)\n    }\n\n    let { isOnDemandRevalidate, revalidateOnlyGenerated } =\n      checkIsOnDemandRevalidate(req, this.renderOpts.previewProps)\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    const handleRedirect = (pageData: any) => {\n      const redirect = {\n        destination: pageData.pageProps.__N_REDIRECT,\n        statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n        basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n      }\n      const statusCode = getRedirectStatus(redirect)\n      const { basePath } = this.nextConfig\n\n      if (\n        basePath &&\n        redirect.basePath !== false &&\n        redirect.destination.startsWith('/')\n      ) {\n        redirect.destination = `${basePath}${redirect.destination}`\n      }\n\n      if (redirect.destination.startsWith('/')) {\n        redirect.destination = normalizeRepeatedSlashes(redirect.destination)\n      }\n\n      res\n        .redirect(redirect.destination, statusCode)\n        .body(redirect.destination)\n        .send()\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    let ssgCacheKey: string | null = null\n    if (\n      !isPreviewMode &&\n      isSSG &&\n      !opts.supportsDynamicResponse &&\n      !isServerAction &&\n      !minimalPostponed &&\n      !isDynamicRSCRequest\n    ) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${\n        (pathname === '/' || resolvedUrlPathname === '/') && locale\n          ? ''\n          : resolvedUrlPathname\n      }${query.amp ? '.amp' : ''}`\n    }\n\n    if ((is404Page || is500Page) && isSSG) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${\n        query.amp ? '.amp' : ''\n      }`\n    }\n\n    if (ssgCacheKey) {\n      ssgCacheKey = decodePathParams(ssgCacheKey)\n\n      // ensure /index and / is normalized to one key\n      ssgCacheKey =\n        ssgCacheKey === '/index' && pathname === '/' ? '/' : ssgCacheKey\n    }\n    let protocol: 'http:' | 'https:' = 'https:'\n\n    try {\n      const parsedFullUrl = new URL(\n        getRequestMeta(req, 'initURL') || '/',\n        'http://n'\n      )\n      protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n    } catch {}\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      (globalThis as any).__incrementalCache ||\n      (await this.getIncrementalCache({\n        requestHeaders: Object.assign({}, req.headers),\n        requestProtocol: protocol.substring(0, protocol.length - 1) as\n          | 'http'\n          | 'https',\n      }))\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    type RendererContext = {\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      pagesFallback: boolean | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }\n    type Renderer = (\n      context: RendererContext\n    ) => Promise<ResponseCacheEntry | null>\n\n    const doRender: Renderer = async ({\n      postponed,\n      pagesFallback = false,\n      fallbackRouteParams,\n    }) => {\n      // In development, we always want to generate dynamic HTML.\n      let supportsDynamicResponse: boolean =\n        // If we're in development, we always support dynamic HTML, unless it's\n        // a data request, in which case we only produce static HTML.\n        (!isNextDataRequest && opts.dev === true) ||\n        // If this is not SSG or does not have static paths, then it supports\n        // dynamic HTML.\n        (!isSSG && !hasGetStaticPaths) ||\n        // If this request has provided postponed data, it supports dynamic\n        // HTML.\n        typeof postponed === 'string' ||\n        // If this is a dynamic RSC request, then this render supports dynamic\n        // HTML (it's dynamic).\n        isDynamicRSCRequest\n\n      const origQuery = parseUrl(req.url || '', true).query\n\n      // clear any dynamic route params so they aren't in\n      // the resolvedUrl\n      if (opts.params) {\n        Object.keys(opts.params).forEach((key) => {\n          delete origQuery[key]\n        })\n      }\n      const hadTrailingSlash =\n        urlPathname !== '/' && this.nextConfig.trailingSlash\n\n      const resolvedUrl = formatUrl({\n        pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,\n        // make sure to only add query values from original URL\n        query: origQuery,\n      })\n\n      // When html bots request PPR page, perform the full dynamic rendering.\n      const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n      const renderOpts: LoadedRenderOpts = {\n        ...components,\n        ...opts,\n        ...(isAppPath\n          ? {\n              incrementalCache,\n              // This is a revalidation request if the request is for a static\n              // page and it is not being resumed from a postponed render and\n              // it is not a dynamic RSC request then it is a revalidation\n              // request.\n              isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n              serverActions: this.nextConfig.experimental.serverActions,\n            }\n          : {}),\n        isNextDataRequest,\n        resolvedUrl,\n        locale,\n        locales,\n        defaultLocale,\n        multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n        // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on\n        // asPath\n        resolvedAsPath:\n          hasServerProps || hasGetInitialProps\n            ? formatUrl({\n                // we use the original URL pathname less the _next/data prefix if\n                // present\n                pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,\n                query: origQuery,\n              })\n            : resolvedUrl,\n        experimental: {\n          ...opts.experimental,\n          isRoutePPREnabled,\n        },\n        supportsDynamicResponse,\n        shouldWaitOnAllReady,\n        isOnDemandRevalidate,\n        isDraftMode: isPreviewMode,\n        isServerAction,\n        postponed,\n        waitUntil: this.getWaitUntil(),\n        onClose: res.onClose.bind(res),\n        onAfterTaskError: undefined,\n        // only available in dev\n        setIsrStatus: (this as any).setIsrStatus,\n      }\n\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        supportsDynamicResponse = false\n        renderOpts.nextExport = true\n        renderOpts.supportsDynamicResponse = false\n        renderOpts.isStaticGeneration = true\n        renderOpts.isRevalidate = true\n        renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses\n      }\n\n      // Legacy render methods will return a render result that needs to be\n      // served by the server.\n      let result: RenderResult\n\n      if (routeModule) {\n        if (isAppRouteRouteModule(routeModule)) {\n          if (\n            // The type check here ensures that `req` is correctly typed, and the\n            // environment variable check provides dead code elimination.\n            process.env.NEXT_RUNTIME === 'edge' ||\n            !isNodeNextRequest(req) ||\n            !isNodeNextResponse(res)\n          ) {\n            throw new Error(\n              'Invariant: App Route Route Modules cannot be used in the edge runtime'\n            )\n          }\n\n          const context: AppRouteRouteHandlerContext = {\n            params: opts.params,\n            prerenderManifest,\n            renderOpts: {\n              experimental: {\n                dynamicIO: renderOpts.experimental.dynamicIO,\n                authInterrupts: renderOpts.experimental.authInterrupts,\n              },\n              supportsDynamicResponse,\n              incrementalCache,\n              cacheLifeProfiles: this.nextConfig.experimental?.cacheLife,\n              isRevalidate: isSSG,\n              waitUntil: this.getWaitUntil(),\n              onClose: res.onClose.bind(res),\n              onAfterTaskError: undefined,\n              onInstrumentationRequestError:\n                this.renderOpts.onInstrumentationRequestError,\n            },\n            sharedContext: {\n              buildId: this.buildId,\n            },\n          }\n\n          try {\n            const request = NextRequestAdapter.fromNodeNextRequest(\n              req,\n              signalFromNodeResponse(res.originalResponse)\n            )\n\n            const response = await routeModule.handle(request, context)\n\n            ;(req as any).fetchMetrics = (\n              context.renderOpts as any\n            ).fetchMetrics\n\n            const cacheTags = context.renderOpts.collectedTags\n\n            // If the request is for a static response, we can cache it so long\n            // as it's not edge.\n            if (isSSG) {\n              const blob = await response.blob()\n\n              // Copy the headers from the response.\n              const headers = toNodeOutgoingHttpHeaders(response.headers)\n\n              if (cacheTags) {\n                headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n              }\n\n              if (!headers['content-type'] && blob.type) {\n                headers['content-type'] = blob.type\n              }\n\n              const revalidate =\n                typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n                context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n                  ? false\n                  : context.renderOpts.collectedRevalidate\n\n              const expire =\n                typeof context.renderOpts.collectedExpire === 'undefined' ||\n                context.renderOpts.collectedExpire >= INFINITE_CACHE\n                  ? undefined\n                  : context.renderOpts.collectedExpire\n\n              // Create the cache entry for the response.\n              const cacheEntry: ResponseCacheEntry = {\n                value: {\n                  kind: CachedRouteKind.APP_ROUTE,\n                  status: response.status,\n                  body: Buffer.from(await blob.arrayBuffer()),\n                  headers,\n                },\n                cacheControl: { revalidate, expire },\n                isFallback: false,\n              }\n\n              return cacheEntry\n            }\n            let pendingWaitUntil = context.renderOpts.pendingWaitUntil\n\n            // Attempt using provided waitUntil if available\n            // if it's not we fallback to sendResponse's handling\n            if (pendingWaitUntil) {\n              if (context.renderOpts.waitUntil) {\n                context.renderOpts.waitUntil(pendingWaitUntil)\n                pendingWaitUntil = undefined\n              }\n            }\n\n            // Send the response now that we have copied it into the cache.\n            await sendResponse(\n              req,\n              res,\n              response,\n              context.renderOpts.pendingWaitUntil\n            )\n            return null\n          } catch (err) {\n            await this.instrumentationOnRequestError(err, req, {\n              routerKind: 'App Router',\n              routePath: pathname,\n              routeType: 'route',\n              revalidateReason: getRevalidateReason(renderOpts),\n            })\n\n            // If this is during static generation, throw the error again.\n            if (isSSG) throw err\n\n            Log.error(err)\n\n            // Otherwise, send a 500 response.\n            await sendResponse(req, res, new Response(null, { status: 500 }))\n\n            return null\n          }\n        } else if (\n          isPagesRouteModule(routeModule) ||\n          isAppPageRouteModule(routeModule)\n        ) {\n          // An OPTIONS request to a page handler is invalid.\n          if (req.method === 'OPTIONS' && !is404Page) {\n            await sendResponse(req, res, new Response(null, { status: 400 }))\n            return null\n          }\n\n          if (isPagesRouteModule(routeModule)) {\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend\n            // the object here but only updating its `clientReferenceManifest` and\n            // `nextFontManifest` properties.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n            renderOpts.clientReferenceManifest =\n              components.clientReferenceManifest\n\n            const request = isNodeNextRequest(req) ? req.originalRequest : req\n            const response = isNodeNextResponse(res)\n              ? res.originalResponse\n              : res\n\n            // Call the built-in render method on the module.\n            try {\n              result = await routeModule.render(\n                request as any,\n                response as any,\n                {\n                  page: pathname,\n                  params: opts.params,\n                  query,\n                  renderOpts,\n                  sharedContext: {\n                    buildId: this.buildId,\n                    deploymentId: this.nextConfig.deploymentId,\n                    customServer: this.serverOptions.customServer || undefined,\n                  },\n                  renderContext: {\n                    isFallback: pagesFallback,\n                    isDraftMode: renderOpts.isDraftMode,\n                    developmentNotFoundSourcePage: getRequestMeta(\n                      req,\n                      'developmentNotFoundSourcePage'\n                    ),\n                  },\n                }\n              )\n            } catch (err) {\n              await this.instrumentationOnRequestError(err, req, {\n                routerKind: 'Pages Router',\n                routePath: pathname,\n                routeType: 'render',\n                revalidateReason: getRevalidateReason({\n                  isRevalidate: isSSG,\n                  isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n                }),\n              })\n              throw err\n            }\n          } else {\n            const module = components.routeModule as AppPageRouteModule\n\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n            // object here but only updating its `nextFontManifest` field.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n\n            const context: AppPageRouteHandlerContext = {\n              page: is404Page ? '/404' : pathname,\n              params: opts.params,\n              query,\n              fallbackRouteParams,\n              renderOpts,\n              serverComponentsHmrCache: this.getServerComponentsHmrCache(),\n              sharedContext: {\n                buildId: this.buildId,\n              },\n            }\n\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (\n              this.nextConfig.experimental.dynamicIO &&\n              this.renderOpts.dev &&\n              !isPrefetchRSCRequest &&\n              !isServerAction\n            ) {\n              const warmup = await module.warmup(req, res, context)\n\n              // If the warmup is successful, we should use the resume data\n              // cache from the warmup.\n              if (warmup.metadata.devRenderResumeDataCache) {\n                renderOpts.devRenderResumeDataCache =\n                  warmup.metadata.devRenderResumeDataCache\n              }\n            }\n\n            // Call the built-in render method on the module.\n            result = await module.render(req, res, context)\n          }\n        } else {\n          throw new Error('Invariant: Unknown route module type')\n        }\n      } else {\n        // If we didn't match a page, we should fallback to using the legacy\n        // render method.\n        result = await this.renderHTML(req, res, pathname, query, renderOpts)\n      }\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isAppPath &&\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !this.renderOpts.dev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${urlPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      // Based on the metadata, we can determine what kind of cache result we\n      // should return.\n\n      // Handle `isNotFound`.\n      if ('isNotFound' in metadata && metadata.isNotFound) {\n        return {\n          value: null,\n          cacheControl,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isRedirect`.\n      if (metadata.isRedirect) {\n        return {\n          value: {\n            kind: CachedRouteKind.REDIRECT,\n            props: metadata.pageData ?? metadata.flightData,\n          } satisfies CachedRedirectValue,\n          cacheControl,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isNull`.\n      if (result.isNull) {\n        return null\n      }\n\n      // We now have a valid HTML result that we can return to the user.\n      if (isAppPath) {\n        return {\n          value: {\n            kind: CachedRouteKind.APP_PAGE,\n            html: result,\n            headers,\n            rscData: metadata.flightData,\n            postponed: metadata.postponed,\n            status: res.statusCode,\n            segmentData: metadata.segmentData,\n          } satisfies CachedAppPageValue,\n          cacheControl,\n          isFallback: !!fallbackRouteParams,\n        } satisfies ResponseCacheEntry\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.PAGES,\n          html: result,\n          pageData: metadata.pageData ?? metadata.flightData,\n          headers,\n          status: isAppPath ? res.statusCode : undefined,\n        } satisfies CachedPageValue,\n        cacheControl,\n        isFallback: pagesFallback,\n      }\n    }\n\n    let responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n    }): Promise<ResponseCacheEntry | null> => {\n      const isProduction = !this.renderOpts.dev\n      const didRespond = hasResolved || res.sent\n\n      // If we haven't found the static paths for the route, then do it now.\n      if (!staticPaths && isDynamic) {\n        if (hasGetStaticPaths) {\n          const pathsResult = await this.getStaticPaths({\n            pathname,\n            requestHeaders: req.headers,\n            isAppPath,\n            page: components.page,\n          })\n\n          staticPaths = pathsResult.staticPaths\n          fallbackMode = pathsResult.fallbackMode\n        } else {\n          staticPaths = undefined\n          fallbackMode = FallbackMode.NOT_FOUND\n        }\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (\n        fallbackMode === FallbackMode.PRERENDER &&\n        isBot(req.headers['user-agent'] || '')\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !this.minimalMode\n      ) {\n        await this.render404(req, res)\n        return null\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // We use `ssgCacheKey` here as it is normalized to match the encoding\n      // from getStaticPaths along with including the locale.\n      //\n      // We use the `resolvedUrlPathname` for the development case when this\n      // is an app path since it doesn't include locale information.\n      //\n      // We decode the `resolvedUrlPathname` to correctly match the app path\n      // with prerendered paths.\n      let staticPathKey = ssgCacheKey\n      if (!staticPathKey && opts.dev && isAppPath) {\n        staticPathKey = decodePathParams(resolvedUrlPathname)\n      }\n      if (staticPathKey && query.amp) {\n        staticPathKey = staticPathKey.replace(/\\.amp$/, '')\n      }\n\n      const isPageIncludedInStaticPaths =\n        staticPathKey && staticPaths?.includes(staticPathKey)\n\n      // When experimental compile is used, no pages have been prerendered,\n      // so they should all be blocking.\n\n      // @ts-expect-error internal field\n      if (this.nextConfig.experimental.isExperimentalCompile) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // When we did not respond from cache, we need to choose to block on\n      // rendering or return a skeleton.\n      //\n      // - Data requests always block.\n      // - Blocking mode fallback always blocks.\n      // - Preview mode toggles all pages to be resolved in a blocking manner.\n      // - Non-dynamic pages should block (though this is an impossible\n      //   case in production).\n      // - Dynamic pages should return their skeleton if not defined in\n      //   getStaticPaths, then finish the data request on the client-side.\n      //\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        !this.minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isPreviewMode &&\n        isDynamic &&\n        (isProduction || !staticPaths || !isPageIncludedInStaticPaths)\n      ) {\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || (staticPaths && staticPaths?.length > 0)) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        // If this is a pages router page.\n        if (isPagesRouteModule(components.routeModule) && !isNextDataRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? (locale ? `/${locale}${pathname}` : pathname) : null,\n            // This is the response generator for the fallback shell.\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              // For the pages router, fallbacks cannot be revalidated or\n              // generated in production. In the case of a missing fallback,\n              // we return null, but if it's being revalidated, we just return\n              // the previous fallback cache entry. This preserves the previous\n              // behavior.\n              if (isProduction) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n\n              // We pass `undefined` and `null` as it doesn't apply to the pages\n              // router.\n              return doRender({\n                postponed: undefined,\n                // For the pages router, fallbacks can only be generated on\n                // demand in development, so if we're not in production, and we\n                // aren't a app path.\n                pagesFallback: true,\n                fallbackRouteParams: null,\n              })\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n        // If this is a app router page, PPR is enabled, and PFPR is also\n        // enabled, then we should use the fallback renderer.\n        else if (\n          isRoutePPREnabled &&\n          isAppPageRouteModule(components.routeModule) &&\n          !isRSCRequest\n        ) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? pathname : null,\n            // This is the response generator for the fallback shell.\n            async () =>\n              doRender({\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                pagesFallback: undefined,\n                fallbackRouteParams:\n                  // If we're in production of we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(pathname)\n                    : null,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n\n        // If the fallback response was set to null, then we should return null.\n        if (fallbackResponse === null) return null\n\n        // Otherwise, if we did get a fallback response, we should return it.\n        if (fallbackResponse) {\n          // Remove the cache control from the response to prevent it from being\n          // used in the surrounding cache.\n          delete fallbackResponse.cacheControl\n\n          return fallbackResponse\n        }\n      }\n\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          isFallback: false,\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        isDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'didSetDefaultRouteMatches') ||\n          isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        postponed,\n        pagesFallback: undefined,\n        fallbackRouteParams,\n      })\n    }\n\n    const cacheEntry = await this.responseCache.get(\n      ssgCacheKey,\n      responseGenerator,\n      {\n        routeKind:\n          // If the route module is not defined, we can assume it's a page being\n          // rendered and thus check isAppPath.\n          routeModule?.definition.kind ??\n          (isAppPath ? RouteKind.APP_PAGE : RouteKind.PAGES),\n        incrementalCache,\n        isOnDemandRevalidate,\n        isPrefetch: req.headers.purpose === 'prefetch',\n        isRoutePPREnabled,\n      }\n    )\n\n    if (isPreviewMode) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    if (!cacheEntry) {\n      if (ssgCacheKey && !(isOnDemandRevalidate && revalidateOnlyGenerated)) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n      return null\n    }\n\n    // If we're not in minimal mode and the cache entry that was returned was a\n    // app page fallback, then we need to kick off the dynamic shell generation.\n    if (\n      ssgCacheKey &&\n      !this.minimalMode &&\n      isRoutePPREnabled &&\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      cacheEntry.isFallback &&\n      !isOnDemandRevalidate &&\n      // When we're debugging the fallback shell, we don't want to regenerate\n      // the route shell.\n      !isDebugFallbackShell &&\n      process.env.DISABLE_ROUTE_SHELL_GENERATION !== 'true'\n    ) {\n      scheduleOnNextTick(async () => {\n        try {\n          await this.responseCache.get(\n            ssgCacheKey,\n            () =>\n              doRender({\n                // We're an on-demand request, so we don't need to pass in the\n                // fallbackRouteParams.\n                fallbackRouteParams: null,\n                pagesFallback: undefined,\n                postponed: undefined,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isOnDemandRevalidate: true,\n              isPrefetch: false,\n              isRoutePPREnabled: true,\n            }\n          )\n        } catch (err) {\n          console.error('Error occurred while rendering dynamic shell', err)\n        }\n      })\n    }\n\n    const didPostpone =\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      typeof cacheEntry.value.postponed === 'string'\n\n    if (\n      isSSG &&\n      // We don't want to send a cache header for requests that contain dynamic\n      // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n      // request, then we should set the cache header.\n      !isDynamicRSCRequest &&\n      (!didPostpone || isPrefetchRSCRequest)\n    ) {\n      if (!this.minimalMode) {\n        // set x-nextjs-cache header to match the header\n        // we set for the image-optimizer\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n      // Set a header used by the client router to signal the response is static\n      // and should respect the `static` cache staleTime value.\n      res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n    }\n\n    const { value: cachedData } = cacheEntry\n\n    if (\n      typeof segmentPrefetchHeader === 'string' &&\n      cachedData?.kind === CachedRouteKind.APP_PAGE &&\n      cachedData.segmentData\n    ) {\n      // This is a prefetch request issued by the client Segment Cache. These\n      // should never reach the application layer (lambda). We should either\n      // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n      // Set a header to indicate that PPR is enabled for this route. This\n      // lets the client distinguish between a regular cache miss and a cache\n      // miss due to PPR being disabled. In other contexts this header is used\n      // to indicate that the response contains dynamic data, but here we're\n      // only using it to indicate that the feature is enabled — the segment\n      // response itself contains whether the data is dynamic.\n      res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n      const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n      if (matchedSegment !== undefined) {\n        // Cache hit\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(matchedSegment),\n          // TODO: Eventually this should use cache control of the individual\n          // segment, not the whole page.\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // Cache miss. Either a cache entry for this route has not been generated\n      // (which technically should not be possible when PPR is enabled, because\n      // at a minimum there should always be a fallback entry) or there's no\n      // match for the requested segment. Respond with a 204 No Content. We\n      // don't bother to respond with 404, because these requests are only\n      // issued as part of a prefetch.\n      res.statusCode = 204\n      return {\n        type: 'rsc',\n        body: RenderResult.fromStatic(''),\n        cacheControl: cacheEntry?.cacheControl,\n      }\n    }\n\n    // If the cache value is an image, we should error early.\n    if (cachedData?.kind === CachedRouteKind.IMAGE) {\n      throw new InvariantError('SSG should not return an image cache value')\n    }\n\n    // Coerce the cache control parameter from the render.\n    let cacheControl: CacheControl | undefined\n\n    // If this is a resume request in minimal mode it is streamed with dynamic\n    // content and should not be cached.\n    if (minimalPostponed) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    }\n\n    // If this is in minimal mode and this is a flight request that isn't a\n    // prefetch request while PPR is enabled, it cannot be cached as it contains\n    // dynamic content.\n    else if (\n      this.minimalMode &&\n      isRSCRequest &&\n      !isPrefetchRSCRequest &&\n      isRoutePPREnabled\n    ) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    } else if (!this.renderOpts.dev || (hasServerProps && !isNextDataRequest)) {\n      // If this is a preview mode request, we shouldn't cache it\n      if (isPreviewMode) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this isn't SSG, then we should set change the header only if it is\n      // not set already.\n      else if (!isSSG) {\n        if (!res.getHeader('Cache-Control')) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n      }\n\n      // If we are rendering the 404 page we derive the cache-control\n      // revalidate period from the value that trigged the not found\n      // to be rendered. So if `getStaticProps` returns\n      // { notFound: true, revalidate 60 } the revalidate period should\n      // be 60 but if a static asset 404s directly it should have a revalidate\n      // period of 0 so that it doesn't get cached unexpectedly by a CDN\n      else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n\n        cacheControl = {\n          revalidate:\n            typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n          expire: undefined,\n        }\n      } else if (is500Page) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (cacheEntry.cacheControl) {\n        // If the cache entry has a cache control with a revalidate value that's\n        // a number, use it.\n        if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n          if (cacheEntry.cacheControl.revalidate < 1) {\n            throw new Error(\n              `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n            )\n          }\n\n          cacheControl = {\n            revalidate: cacheEntry.cacheControl.revalidate,\n            expire:\n              cacheEntry.cacheControl?.expire ?? this.nextConfig.expireTime,\n          }\n        }\n        // Otherwise if the revalidate value is false, then we should use the\n        // cache time of one year.\n        else {\n          cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n        }\n      }\n    }\n\n    cacheEntry.cacheControl = cacheControl\n\n    // If there's a callback for `onCacheEntry`, call it with the cache entry\n    // and the revalidate options.\n    const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n    if (onCacheEntry) {\n      const finished = await onCacheEntry(\n        {\n          ...cacheEntry,\n          // TODO: remove this when upstream doesn't\n          // always expect this value to be \"PAGE\"\n          value: {\n            ...cacheEntry.value,\n            kind:\n              cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n                ? 'PAGE'\n                : cacheEntry.value?.kind,\n          },\n        },\n        {\n          url: getRequestMeta(req, 'initURL'),\n        }\n      )\n      if (finished) {\n        // TODO: maybe we have to end the request?\n        return null\n      }\n    }\n\n    if (!cachedData) {\n      // add revalidate metadata before rendering 404 page\n      // so that we can use this as source of truth for the\n      // cache-control header instead of what the 404 page returns\n      // for the revalidate value\n      addRequestMeta(\n        req,\n        'notFoundRevalidate',\n        cacheEntry.cacheControl?.revalidate\n      )\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n      if (isNextDataRequest) {\n        res.statusCode = 404\n        res.body('{\"notFound\":true}').send()\n        return null\n      }\n\n      if (this.renderOpts.dev) {\n        addRequestMeta(req, 'developmentNotFoundSourcePage', pathname)\n      }\n      await this.render404(req, res, { pathname, query }, false)\n      return null\n    } else if (cachedData.kind === CachedRouteKind.REDIRECT) {\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      if (isNextDataRequest) {\n        return {\n          type: 'json',\n          body: RenderResult.fromStatic(\n            // @TODO: Handle flight data.\n            JSON.stringify(cachedData.props)\n          ),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      } else {\n        await handleRedirect(cachedData.props)\n        return null\n      }\n    } else if (cachedData.kind === CachedRouteKind.APP_ROUTE) {\n      const headers = fromNodeOutgoingHttpHeaders(cachedData.headers)\n\n      if (!(this.minimalMode && isSSG)) {\n        headers.delete(NEXT_CACHE_TAGS_HEADER)\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        cacheEntry.cacheControl &&\n        !res.getHeader('Cache-Control') &&\n        !headers.get('Cache-Control')\n      ) {\n        headers.set(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      await sendResponse(\n        req,\n        res,\n        new Response(cachedData.body, {\n          headers,\n          status: cachedData.status || 200,\n        })\n      )\n      return null\n    } else if (cachedData.kind === CachedRouteKind.APP_PAGE) {\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!this.minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      if (\n        this.minimalMode &&\n        isSSG &&\n        cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      ) {\n        res.setHeader(\n          NEXT_CACHE_TAGS_HEADER,\n          cachedData.headers[NEXT_CACHE_TAGS_HEADER] as string\n        )\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isPreviewMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return {\n            type: 'rsc',\n            body: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          }\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(cachedData.rscData),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || this.minimalMode) {\n        return {\n          type: 'html',\n          body,\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return {\n          type: 'html',\n          body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        }\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        postponed: cachedData.postponed,\n        pagesFallback: undefined,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return {\n        type: 'html',\n        body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      }\n    } else if (isNextDataRequest) {\n      return {\n        type: 'json',\n        body: RenderResult.fromStatic(JSON.stringify(cachedData.pageData)),\n        cacheControl: cacheEntry.cacheControl,\n      }\n    } else {\n      return {\n        type: 'html',\n        body: cachedData.html,\n        cacheControl: cacheEntry.cacheControl,\n      }\n    }\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      locale: getRequestMeta(ctx.req, 'locale'),\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): Promise<MiddlewareRoutingItem | undefined>\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { req, res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback =\n      getRequestMeta(ctx.req, 'bubbleNoFallback') ?? false\n    delete query[NEXT_RSC_UNION_QUERY]\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromRequest(req, pathname),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        addRequestMeta(ctx.req, 'customErrorRender', true)\n        await this.renderErrorToResponse(ctx, err)\n        removeRequestMeta(ctx.req, 'customErrorRender')\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (\n          (this.minimalMode && process.env.NEXT_RUNTIME !== 'edge') ||\n          this.renderOpts.dev\n        ) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    const middleware = await this.getMiddleware()\n    if (\n      middleware &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      const locale = getRequestMeta(req, 'locale')\n\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('content-type', 'application/json')\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic(''),\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !getRequestMeta(ctx.req, 'customErrorRender') &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          locale: getRequestMeta(ctx.req, 'locale'),\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            type: 'html',\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic('Internal Server Error'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    // Ensure the locales are provided on the request meta.\n    if (this.nextConfig.i18n) {\n      if (!getRequestMeta(req, 'locale')) {\n        addRequestMeta(req, 'locale', this.nextConfig.i18n.defaultLocale)\n      }\n      addRequestMeta(req, 'defaultLocale', this.nextConfig.i18n.defaultLocale)\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["NoFallbackError", "WrappedBuildError", "Server", "Error", "constructor", "innerError", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "segmentPrefetchRSC", "match", "result", "extract", "originalPathname", "segmentPath", "headers", "RSC_HEADER", "toLowerCase", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "addRequestMeta", "prefetchRSC", "normalize", "rsc", "stripFlightHeaders", "segmentPrefetchRSCRequest", "url", "parsed", "parseUrl", "formatUrl", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "matchNextDataPathname", "path", "buildId", "process", "env", "NEXT_RUNTIME", "getRequestMeta", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "getRouteFromAssetPath", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "removeRequestMeta", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "execOnce", "Log", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "formatHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "I18NProvider", "localeNormalizer", "LocaleRouteNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "checkIsAppPPREnabled", "ppr", "isAppSegmentPrefetchEnabled", "clientSegmentCache", "RSCPathnameNormalizer", "PrefetchRSCPathnameNormalizer", "SegmentPrefixRSCPathnameNormalizer", "NextDataPathnameNormalizer", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "strictNextHead", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "htmlLimitedBots", "expireTime", "clientTraceMetadata", "dynamicIO", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "setConfig", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "reloadMatchers", "manifest<PERSON><PERSON>der", "ServerManifestLoader", "name", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "DefaultRouteMatcherManager", "PagesRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "NextRequestHint", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "getTracer", "withPropagatedContext", "trace", "BaseServerSpan", "spanName", "kind", "SpanKind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "patchSetHeaderWithCookieSupport", "isNodeNextResponse", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "normalizeRepeatedSlashes", "redirect", "body", "send", "query", "URLSearchParams", "isNodeNextRequest", "xForwardedProto", "isHttps", "socket", "encrypted", "toString", "remoteAddress", "attachRequestMeta", "getHostname", "parseUrlUtil", "replace", "pathnameInfo", "getNextPathnameInfo", "removePathPrefix", "useMatchedPathHeader", "MATCHED_PATH_HEADER", "<PERSON><PERSON><PERSON>", "URL", "urlPathname", "NEXT_RESUME_HEADER", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "localeAnalysisResult", "inferredFromDefault", "denormalizePagePath", "srcPathname", "pageIsDynamic", "isDynamicRoute", "definition", "utils", "getUtils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "pathnameBeforeRewrite", "rewriteParamKeys", "handleRewrites", "didRewrite", "queryParams", "key", "value", "normalizedKey", "normalizeNextQueryParam", "paramsResult", "normalizeDynamicRouteParams", "hasValidParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "routeMatchesHeader", "routeMatches", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeVercelUrl", "defaultRouteRegex", "groups", "DecodeError", "NormalizeError", "renderError", "Boolean", "webServerConfig", "protocol", "parsedFullUrl", "incrementalCache", "getIncrementalCache", "requestHeaders", "requestProtocol", "resetRequestCache", "__incrementalCache", "handlers", "getCacheHandlers", "header", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "expiredTags", "promises", "handler", "receiveExpiredTags", "Promise", "all", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "normalizeLocalePath", "response", "Response", "bubble", "run", "code", "isBubbledError", "getProperError", "getRequestHandlerWithMetadata", "meta", "getRequestHandler", "setRequestMeta", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "normalizeAppPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "ua", "isBotRequest", "isBot", "botType", "getBotType", "serveStreamingMetadata", "shouldServeStreamingMetadata", "payload", "originalStatus", "type", "cacheControl", "sent", "<PERSON><PERSON><PERSON><PERSON>", "expire", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "getBuiltinRequestContext", "waitUntil", "getInternalWaitUntil", "startsWith", "customServer", "hasPage", "isBlockedPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "parseFallbackField", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "isInterceptionRouteAppPath", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "NEXT_ROUTER_STATE_TREE_HEADER", "addedNextUrlToVary", "append<PERSON><PERSON>er", "NEXT_URL", "opts", "components", "prerenderManifest", "cacheEntry", "UNDERSCORE_NOT_FOUND_ROUTE", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "hasGetStaticPaths", "isServerAction", "getIsServerAction", "hasGetInitialProps", "Component", "getInitialProps", "isSSG", "getStaticProps", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "isDynamic", "pathsResult", "resolvedWithoutSlash", "removeTrailingSlash", "includes", "routes", "toRoute", "isNextDataRequest", "isPrefetchRSCRequest", "routeModule", "couldSupportPPR", "isAppPageRouteModule", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "isDynamicRSCRequest", "segmentPrefetchHeader", "isHtmlBot", "isHtmlBotRequest", "STATIC_STATUS_PAGES", "parseInt", "slice", "RenderResult", "fromStatic", "isSupportedDocument", "Document", "NEXT_BUILTIN_DOCUMENT", "previewData", "isPreviewMode", "tryGetPreviewData", "multiZoneDraftMode", "isEdgeRuntime", "runtime", "isOnDemandRevalidate", "revalidateOnlyGenerated", "checkIsOnDemandRevalidate", "handleRedirect", "pageData", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "getRedirectStatus", "ssgCacheKey", "decodePathParams", "doR<PERSON>", "pagesFallback", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hadTrailingSlash", "resolvedUrl", "shouldWaitOnAllReady", "isRevalidate", "serverActions", "resolvedAsPath", "isDraftMode", "onClose", "onAfterTaskError", "setIsrStatus", "nextExport", "isStaticGeneration", "isAppRouteRouteModule", "context", "sharedContext", "request", "NextRequestAdapter", "fromNodeNextRequest", "signalFromNodeResponse", "handle", "fetchMetrics", "cacheTags", "collectedTags", "blob", "toNodeOutgoingHttpHeaders", "NEXT_CACHE_TAGS_HEADER", "revalidate", "collectedRevalidate", "INFINITE_CACHE", "collectedExpire", "CachedRouteKind", "APP_ROUTE", "status", "from", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON>", "pendingWaitUntil", "sendResponse", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "isPagesRouteModule", "clientReferenceManifest", "renderContext", "developmentNotFoundSourcePage", "module", "warmup", "metadata", "devRenderResumeDataCache", "renderHTML", "fetchTags", "staticBailoutInfo", "description", "stack", "message", "indexOf", "isNotFound", "isRedirect", "REDIRECT", "props", "flightData", "isNull", "APP_PAGE", "html", "rscData", "segmentData", "PAGES", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "FallbackMode", "NOT_FOUND", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "static<PERSON><PERSON><PERSON><PERSON>", "isPageIncludedInStaticPaths", "fallbackResponse", "previousFallbackCacheEntry", "toResponseCacheEntry", "routeKind", "RouteKind", "getFallbackRouteParams", "isPrefetch", "purpose", "DISABLE_ROUTE_SHELL_GENERATION", "scheduleOnNextTick", "didPostpone", "isMiss", "NEXT_IS_PRERENDER_HEADER", "cachedData", "NEXT_DID_POSTPONE_HEADER", "matchedSegment", "IMAGE", "InvariantError", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "CACHE_ONE_YEAR", "onCacheEntry", "getCacheControlHeader", "JSON", "stringify", "fromNodeOutgoingHttpHeaders", "delete", "set", "Array", "isArray", "v", "chain", "ReadableStream", "start", "controller", "enqueue", "ENCODED_TAGS", "CLOSED", "BODY_AND_HTML", "transformer", "TransformStream", "readable", "pipeTo", "writable", "catch", "abort", "e", "stripLocale", "splitPath", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "NEXT_RSC_UNION_QUERY", "fromRequest", "matchAll", "invokeOutput", "MissingStaticPage", "initUrl", "rewroteUrl", "renderErrorToResponse", "isWrappedError", "isError", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "is404", "using404Page", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "statusPage", "NODE_ENV", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": ";;;;;;;;;;;;;;;;IAsTaA,eAAe;eAAfA;;IAIAC,iBAAiB;eAAjBA;;IAoBb,OAwyHC;eAxyH6BC;;;gCAvUvB;+BAsBA;uBAOA;qBA2BgD;gCACxB;gCACG;+BACJ;2BAQvB;wBACwB;0BACW;uCAChB;8BAC+B;wBAE3B;uBACI;qEACT;qCACW;qCACA;6DACf;6BACI;iEACe;6BAMjC;kCAC0B;0BACA;6BACL;0BACa;qCACL;kCAU7B;uCAK+B;4CACK;6CACC;8CACC;8CACA;2CACH;sCACL;wBACe;4BACrB;8BACF;8BACA;wBAKtB;4BAQA;qCAC6B;6BAI7B;uCAC+B;8EACJ;kCACD;qBACK;oCACH;wBAK5B;6BACuC;0BACH;yCACT;oCACS;yBACnB;yBAE8B;gCACN;qBACX;uCAI9B;6BACsB;yBACG;wBACI;2BACV;0BAEuB;wBACZ;2BACF;kCACgB;mCAI5C;0BAC0B;gCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIxB,MAAMF,wBAAwBG;AAAO;AAIrC,MAAMF,0BAA0BE;IAGrCC,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAae,MAAeH;IAkGlBI,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACxD,AAACC,WAAmBC,0BAA0B,GAC9CC;IACN;IAsBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YAqCrB,uBAuEE,mCAQL;aA6DXC,mBAAgE,CACtEC,KACAC,MACAC;gBAII,sCAkBO,+BAWA;YA/BX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,KAAI,uCAAA,IAAI,CAACC,WAAW,CAACC,kBAAkB,qBAAnC,qCAAqCC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClE,MAAMI,SAAS,IAAI,CAACH,WAAW,CAACC,kBAAkB,CAACG,OAAO,CACxDN,UAAUC,QAAQ;gBAEpB,IAAI,CAACI,QAAQ,OAAO;gBAEpB,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGH;gBAC1CL,UAAUC,QAAQ,GAAGM;gBAErB,iDAAiD;gBACjDT,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCb,IAAIW,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,GAAG;gBACzDb,IAAIW,OAAO,CAACI,qDAAmC,CAACF,WAAW,GAAG,GAC5DH;gBAEFM,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;gBACpCgB,IAAAA,2BAAc,EAAChB,KAAK,wBAAwB;gBAC5CgB,IAAAA,2BAAc,EAAChB,KAAK,6BAA6BU;YACnD,OAAO,KAAI,gCAAA,IAAI,CAACN,WAAW,CAACa,WAAW,qBAA5B,8BAA8BX,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClED,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACa,WAAW,CAACC,SAAS,CACzDhB,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCb,IAAIW,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,GAAG;gBACzDG,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;gBACpCgB,IAAAA,2BAAc,EAAChB,KAAK,wBAAwB;YAC9C,OAAO,KAAI,wBAAA,IAAI,CAACI,WAAW,CAACe,GAAG,qBAApB,sBAAsBb,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACe,GAAG,CAACD,SAAS,CACjDhB,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCG,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIW,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;gBACvCS,IAAAA,sCAAkB,EAACpB,IAAIW,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIX,IAAIW,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,KAAK,KAAK;gBACxDG,IAAAA,2BAAc,EAAChB,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIW,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,KAAK,KAAK;oBAClEG,IAAAA,2BAAc,EAAChB,KAAK,wBAAwB;oBAE5C,MAAMqB,4BACJrB,IAAIW,OAAO,CAACI,qDAAmC,CAACF,WAAW,GAAG;oBAChE,IAAI,OAAOQ,8BAA8B,UAAU;wBACjDL,IAAAA,2BAAc,EACZhB,KACA,6BACAqB;oBAEJ;gBACF;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIrB,IAAIsB,GAAG,EAAE;gBACX,MAAMC,SAASC,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG;gBAC/BC,OAAOpB,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIsB,GAAG,GAAGG,IAAAA,WAAS,EAACF;YACtB;YAEA,OAAO;QACT;aAEQG,wBACN,OAAO1B,KAAK2B,KAAKzB;YACf,MAAM0B,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,MAAMC,SAASC,IAAAA,4CAAqB,EAAC7B,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAAC2B,UAAU,CAACA,OAAOE,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAIF,OAAOE,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IACEC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,2BAAc,EAACrC,KAAK,qBACpB;oBACA,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAACsC,SAAS,CAACtC,KAAK2B,KAAKzB;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1B4B,OAAOE,IAAI,CAACO,KAAK;YAEjB,MAAMC,YAAYV,OAAOE,IAAI,CAACF,OAAOE,IAAI,CAACS,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAACtC,KAAK2B,KAAKzB;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAE2B,OAAOE,IAAI,CAACW,IAAI,CAAC,MAAM;YAC1CxC,WAAWyC,IAAAA,8BAAqB,EAACzC,UAAU;YAE3C,iDAAiD;YACjD,IAAIyB,YAAY;gBACd,IAAI,IAAI,CAACpC,UAAU,CAACqD,aAAa,IAAI,CAAC1C,SAASuC,QAAQ,CAAC,MAAM;oBAC5DvC,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAACqD,aAAa,IAC9B1C,SAASsC,MAAM,GAAG,KAClBtC,SAASuC,QAAQ,CAAC,MAClB;oBACAvC,WAAWA,SAAS2C,SAAS,CAAC,GAAG3C,SAASsC,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACM,YAAY,EAAE;oBAEJ/C;gBADjB,gDAAgD;gBAChD,MAAMgD,WAAWhD,wBAAAA,oBAAAA,IAAKW,OAAO,CAACsC,IAAI,qBAAjBjD,kBAAmBkD,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACrC,WAAW;gBAEhE,MAAMsC,eAAe,IAAI,CAACJ,YAAY,CAACK,kBAAkB,CAACJ;gBAC1D,MAAMK,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,KAAI,IAAI,CAACN,YAAY,CAACO,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACR,YAAY,CAACS,OAAO,CAACrD;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAIoD,iBAAiBE,cAAc,EAAE;oBACnCtD,WAAWoD,iBAAiBpD,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChEa,IAAAA,2BAAc,EAAChB,KAAK,UAAUuD,iBAAiBE,cAAc;gBAC7DzC,IAAAA,2BAAc,EAAChB,KAAK,iBAAiBqD;gBAErC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpCC,IAAAA,8BAAiB,EAAC1D,KAAK;gBACzB;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAACuD,iBAAiBE,cAAc,IAAI,CAAC7B,YAAY;oBACnDZ,IAAAA,2BAAc,EAAChB,KAAK,UAAUqD;oBAC9B,MAAM,IAAI,CAACf,SAAS,CAACtC,KAAK2B,KAAKzB;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrBa,IAAAA,2BAAc,EAAChB,KAAK,iBAAiB;YAErC,OAAO;QACT;aAEQ2D,yBAGN,IAAM;aAEAC,8BAGN,IAAM;aAEAC,kCAGN,IAAM;QAqvBV;;;;;;GAMC,QACO3C,YAAY,CAACf;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAAC0D,IAAI,EAAE;gBACzB1D,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAAC0D,IAAI;YACxC;YAEA,2EAA2E;YAC3E,qEAAqE;YACrE,IAAI,IAAI,CAAC1D,WAAW,CAACC,kBAAkB,EAAE;gBACvCD,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACC,kBAAkB;YACtD;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAACD,WAAW,CAACa,WAAW,EAAE;gBAChCb,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACa,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACb,WAAW,CAACe,GAAG,EAAE;gBACxBf,YAAY2D,IAAI,CAAC,IAAI,CAAC3D,WAAW,CAACe,GAAG;YACvC;YAEA,KAAK,MAAM6C,cAAc5D,YAAa;gBACpC,IAAI,CAAC4D,WAAW1D,KAAK,CAACH,WAAW;gBAEjC,OAAO6D,WAAW9C,SAAS,CAACf,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQ8D,6BAGJ,OAAOjE,KAAK2B,KAAKL;YACnB,IAAI4C,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAAC3D,KAAK2B,KAAKL;YAC3D,IAAI4C,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAACxC,qBAAqB,CAAC1B,KAAK2B,KAAKL;gBACtD,IAAI4C,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAgCUG,WAAoB;aACpBC,kBAAwC;aA+uE1CC,uBAAuBC,IAAAA,eAAQ,EAAC;YACtCC,KAAIC,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QA56GE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnB/B,QAAQ,EACRgC,IAAI,EACJC,qBAAqB,EACtB,GAAGnF;QAEJ,IAAI,CAACmF,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGpF;QAErB,IAAI,CAAC6E,GAAG,GACNzC,QAAQC,GAAG,CAACC,YAAY,KAAK,SAASuC,MAAMQ,QAAQ,QAAQC,OAAO,CAACT;QAEtE,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACS,aAAa,CAAC;YAAEP;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACtF,UAAU,GAAGqF;QAClB,IAAI,CAAC7B,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAACsC,aAAa,GAAGC,IAAAA,8BAAc,EAAC,IAAI,CAACvC,QAAQ;QACnD;QACA,IAAI,CAACgC,IAAI,GAAGA;QACZ,IAAI,CAACQ,OAAO,GACVtD,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,IAAI,CAAC5C,UAAU,CAACgG,OAAO,GACvBL,QAAQ,QAAQxC,IAAI,CAAC,IAAI,CAACgC,GAAG,EAAE,IAAI,CAACnF,UAAU,CAACgG,OAAO;QAC5D,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACZ,eAAe,IAAI,CAACa,eAAe;QAExD,IAAI,CAAC7C,YAAY,GAAG,EAAA,wBAAA,IAAI,CAACvD,UAAU,CAACqG,IAAI,qBAApB,sBAAsBC,OAAO,IAC7C,IAAIC,0BAAY,CAAC,IAAI,CAACvG,UAAU,CAACqG,IAAI,IACrChG;QAEJ,yEAAyE;QACzE,IAAI,CAACmG,gBAAgB,GAAG,IAAI,CAACjD,YAAY,GACrC,IAAIkD,4CAAqB,CAAC,IAAI,CAAClD,YAAY,IAC3ClD;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJqG,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC7G,UAAU;QAEnB,IAAI,CAACyC,OAAO,GAAG,IAAI,CAACqE,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBxB,eAAe,CAAC,CAAC7C,QAAQC,GAAG,CAACqE,yBAAyB;QAExD,IAAI,CAACrC,kBAAkB,GAAG,IAAI,CAACsC,qBAAqB,CAAC3B;QAErD,IAAI,CAAC4B,eAAe,GAClB,IAAI,CAACvC,kBAAkB,CAACwC,GAAG,IAC3BC,IAAAA,yBAAoB,EAAC,IAAI,CAACpH,UAAU,CAACC,YAAY,CAACoH,GAAG;QAEvD,IAAI,CAACC,2BAA2B,GAC9B,IAAI,CAAC3C,kBAAkB,CAACwC,GAAG,IAC3B,IAAI,CAACnH,UAAU,CAACC,YAAY,CAACsH,kBAAkB,KAAK;QAEtD,IAAI,CAAC3G,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCe,KACE,IAAI,CAACgD,kBAAkB,CAACwC,GAAG,IAAI,IAAI,CAAC5B,WAAW,GAC3C,IAAIiC,0BAAqB,KACzBnH;YACNoB,aACE,IAAI,CAACyF,eAAe,IAAI,IAAI,CAAC3B,WAAW,GACpC,IAAIkC,0CAA6B,KACjCpH;YACNQ,oBACE,IAAI,CAACyG,2BAA2B,IAAI,IAAI,CAAC/B,WAAW,GAChD,IAAImC,oDAAkC,KACtCrH;YACNiE,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,IAAI+C,oCAA0B,CAAC,IAAI,CAAClF,OAAO,IAC3CpC;QACN;QAEA,IAAI,CAACuH,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAEhD,IAAInF,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvCF,QAAQC,GAAG,CAACmF,kBAAkB,GAAG,IAAI,CAAC9H,UAAU,CAAC+H,YAAY,IAAI;QACnE;QAEA,IAAI,CAACC,UAAU,GAAG;YAChBC,yBAAyB;YACzB5E,eAAe,IAAI,CAACrD,UAAU,CAACqD,aAAa;YAC5C0E,cAAc,IAAI,CAAC/H,UAAU,CAAC+H,YAAY;YAC1CG,gBAAgB,IAAI,CAAClI,UAAU,CAACC,YAAY,CAACiI,cAAc,IAAI;YAC/DC,iBAAiB,IAAI,CAACnI,UAAU,CAACmI,eAAe;YAChDC,eAAe,IAAI,CAACpI,UAAU,CAACqI,GAAG,CAACD,aAAa,IAAI;YACpDvB;YACAyB,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjDC,kBAAkB,GAAE,oCAAA,IAAI,CAACzI,UAAU,CAACC,YAAY,CAACoI,GAAG,qBAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAAC3I,UAAU,CAAC2I,QAAQ;YAClCC,QAAQ,IAAI,CAAC5I,UAAU,CAAC4I,MAAM;YAC9BC,aAAa,IAAI,CAAC7I,UAAU,CAACC,YAAY,CAAC4I,WAAW;YACrDC,kBAAkB,IAAI,CAAC9I,UAAU,CAAC+I,MAAM;YACxCC,mBAAmB,IAAI,CAAChJ,UAAU,CAACC,YAAY,CAAC+I,iBAAiB;YACjEC,yBACE,IAAI,CAACjJ,UAAU,CAACC,YAAY,CAACgJ,uBAAuB;YACtDC,aAAa,GAAE,yBAAA,IAAI,CAAClJ,UAAU,CAACqG,IAAI,qBAApB,uBAAsB8C,OAAO;YAC5CnD,SAAS,IAAI,CAACA,OAAO;YACrBoD,kBAAkB,IAAI,CAACzE,kBAAkB,CAACwC,GAAG;YAC7CkC,mBAAmB,IAAI,CAACrJ,UAAU,CAACC,YAAY,CAACqJ,SAAS;YACzDC,gBAAgB,IAAI,CAACvJ,UAAU,CAACC,YAAY,CAACuJ,KAAK;YAClDC,aAAa,IAAI,CAACzJ,UAAU,CAACyJ,WAAW,GACpC,IAAI,CAACzJ,UAAU,CAACyJ,WAAW,GAC3BpJ;YACJqJ,oBAAoB,IAAI,CAAC1J,UAAU,CAACC,YAAY,CAACyJ,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAClD,qBAAqB1D,MAAM,GAAG,IACtC0D,sBACAtG;YAEN,uDAAuD;YACvDyJ,uBAAuB,IAAI,CAAC9J,UAAU,CAACC,YAAY,CAAC6J,qBAAqB;YACzE,8EAA8E;YAC9EC,iBAAiB,IAAI,CAAC/J,UAAU,CAAC+J,eAAe;YAChD9J,cAAc;gBACZ+J,YAAY,IAAI,CAAChK,UAAU,CAACgK,UAAU;gBACtCC,qBAAqB,IAAI,CAACjK,UAAU,CAACC,YAAY,CAACgK,mBAAmB;gBACrEC,WAAW,IAAI,CAAClK,UAAU,CAACC,YAAY,CAACiK,SAAS,IAAI;gBACrD3C,oBACE,IAAI,CAACvH,UAAU,CAACC,YAAY,CAACsH,kBAAkB,IAAI;gBACrD4C,WAAW,IAAI,CAACnK,UAAU,CAACC,YAAY,CAACkK,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAACpK,UAAU,CAACC,YAAY,CAACmK,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAACxK,UAAU,CAACwK,qBAAqB;QAC9D;QAEA,4DAA4D;QAC5DC,IAAAA,gCAAS,EAAC;YACR/D;YACAC;QACF;QAEA,IAAI,CAAC+D,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAACzE;QACpB,IAAI,CAAC0E,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAEjG;QAAI;IACnD;IAEUkG,iBAAiB;QACzB,OAAO,IAAI,CAACN,QAAQ,CAACE,MAAM;IAC7B;IAmMUD,mBAAwC;QAChD,yEAAyE;QACzE,MAAMM,iBAAiB,IAAIC,0CAAoB,CAAC,CAACC;YAC/C,OAAQA;gBACN,KAAKC,yBAAc;oBACjB,OAAO,IAAI,CAACjB,gBAAgB,MAAM;gBACpC,KAAKkB,6BAAkB;oBACrB,OAAO,IAAI,CAAChB,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,IAAIY,sDAA0B;QAEpE,8BAA8B;QAC9BZ,SAAS3G,IAAI,CACX,IAAIwH,oDAAyB,CAC3B,IAAI,CAAC/F,OAAO,EACZyF,gBACA,IAAI,CAAClI,YAAY;QAIrB,uCAAuC;QACvC2H,SAAS3G,IAAI,CACX,IAAIyH,0DAA4B,CAC9B,IAAI,CAAChG,OAAO,EACZyF,gBACA,IAAI,CAAClI,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACoB,kBAAkB,CAACwC,GAAG,EAAE;YAC/B,gCAAgC;YAChC+D,SAAS3G,IAAI,CACX,IAAI0H,wDAA2B,CAAC,IAAI,CAACjG,OAAO,EAAEyF;YAEhDP,SAAS3G,IAAI,CACX,IAAI2H,0DAA4B,CAAC,IAAI,CAAClG,OAAO,EAAEyF;QAEnD;QAEA,OAAOP;IACT;IAEA,MAAgBZ,8BACd,GAAG6B,IAAqD,EACxD;QACA,MAAM,CAACC,KAAK5L,KAAK6L,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,OAAM,IAAI,CAACA,eAAe,CAACC,cAAc,oBAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,MAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACE5J,MAAMhC,IAAIsB,GAAG,IAAI;oBACjB0K,QAAQhM,IAAIgM,MAAM,IAAI;oBACtB,gEAAgE;oBAChErL,SACEX,eAAeiM,wBAAe,GAC1B7C,OAAO8C,WAAW,CAAClM,IAAIW,OAAO,CAACwL,OAAO,MACtCnM,IAAIW,OAAO;gBACnB,GACAkL;YAEJ,EAAE,OAAOO,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASX,GAAU,EAAQ;QAChC,IAAI,IAAI,CAAChH,KAAK,EAAE;QAChBH,KAAI6H,KAAK,CAACV;IACZ;IAEA,MAAaY,cACXxM,GAAkB,EAClB2B,GAAmB,EACnBzB,SAAkC,EACnB;QACf,MAAM,IAAI,CAACuM,OAAO;QAClB,MAAMT,SAAShM,IAAIgM,MAAM,CAACU,WAAW;QAErC,MAAMC,SAASC,IAAAA,iBAAS;QACxB,OAAOD,OAAOE,qBAAqB,CAAC7M,IAAIW,OAAO,EAAE;YAC/C,OAAOgM,OAAOG,KAAK,CACjBC,0BAAc,CAACP,aAAa,EAC5B;gBACEQ,UAAU,GAAGhB,OAAO,CAAC,EAAEhM,IAAIsB,GAAG,EAAE;gBAChC2L,MAAMC,gBAAQ,CAACC,MAAM;gBACrBC,YAAY;oBACV,eAAepB;oBACf,eAAehM,IAAIsB,GAAG;gBACxB;YACF,GACA,OAAO+L,OACL,IAAI,CAACC,iBAAiB,CAACtN,KAAK2B,KAAKzB,WAAWqN,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,eAAenL,IAAAA,2BAAc,EAACrC,KAAK,mBAAmB;oBAC5DqN,KAAKI,aAAa,CAAC;wBACjB,oBAAoB9L,IAAI+L,UAAU;wBAClC,YAAYF;oBACd;oBAEA,MAAMG,qBAAqBhB,OAAOiB,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvBd,0BAAc,CAACP,aAAa,EAC5B;wBACAH,QAAQ3H,IAAI,CACV,CAAC,2BAA2B,EAAEiJ,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAM3C,OAAOqC,eACT,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAE8B,OAAO,GACxB,GAAG9B,OAAO,CAAC,EAAE8B,OAAO;wBAExBT,KAAKI,aAAa,CAAC;4BACjB,cAAcK;4BACd,cAAcA;4BACd,kBAAkB3C;wBACpB;wBACAkC,KAAKU,UAAU,CAAC5C;oBAClB,OAAO;wBACLkC,KAAKU,UAAU,CACbP,eACI,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAEhM,IAAIsB,GAAG,EAAE,GAC1B,GAAG0K,OAAO,CAAC,EAAEhM,IAAIsB,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAcgM,kBACZtN,GAAkB,EAClB2B,GAAmB,EACnBzB,SAAkC,EACnB;QACf,IAAI;gBAiDK8N,yBAS4BA,0BASd,oBAKY;YAvEjC,qCAAqC;YACrC,MAAM,IAAI,CAACtD,QAAQ,CAACuD,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;YAClDC,IAAAA,+CAA+B,EAC7BlO,KACAmO,IAAAA,2BAAkB,EAACxM,OAAOA,IAAIyM,gBAAgB,GAAGzM;YAGnD,MAAM0M,WAAW,AAACrO,CAAAA,IAAIsB,GAAG,IAAI,EAAC,EAAG4B,KAAK,CAAC,KAAK;YAC5C,MAAMoL,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,8BAAAA,WAAYhO,KAAK,CAAC,cAAc;gBAClC,MAAMiO,WAAWC,IAAAA,+BAAwB,EAACxO,IAAIsB,GAAG;gBACjDK,IAAI8M,QAAQ,CAACF,UAAU,KAAKG,IAAI,CAACH,UAAUI,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACzO,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIsB,GAAG,EAAE;oBACZ,MAAM,qBAAgD,CAAhD,IAAIlC,MAAM,wCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+C;gBACvD;gBAEAc,YAAYsB,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,EAAG;YACjC;YAEA,IAAI,CAACpB,UAAUC,QAAQ,EAAE;gBACvB,MAAM,qBAA+C,CAA/C,IAAIf,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEA,iFAAiF;YACjF,IAAI,OAAOc,UAAU0O,KAAK,KAAK,UAAU;gBACvC1O,UAAU0O,KAAK,GAAGxF,OAAO8C,WAAW,CAClC,IAAI2C,gBAAgB3O,UAAU0O,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAEZ,kBAAkB,IAAI,EAAE,GAAGc,IAAAA,0BAAiB,EAAC9O,OAAOA,MAAM,CAAC;YACnE,MAAM+O,kBAAkBf,mCAAAA,gBAAiBrN,OAAO,CAAC,oBAAoB;YACrE,MAAMqO,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,EAAEf,oCAAAA,0BAAAA,gBAAiBiB,MAAM,qBAAxB,AAACjB,wBAAuCkB,SAAS;YAEvDlP,IAAIW,OAAO,CAAC,mBAAmB,KAAKX,IAAIW,OAAO,CAAC,OAAO,IAAI,IAAI,CAACqC,QAAQ;YACxEhD,IAAIW,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAACqE,IAAI,GACzC,IAAI,CAACA,IAAI,CAACmK,QAAQ,KAClBH,UACE,QACA;YACNhP,IAAIW,OAAO,CAAC,oBAAoB,KAAKqO,UAAU,UAAU;YACzDhP,IAAIW,OAAO,CAAC,kBAAkB,KAAKqN,oCAAAA,2BAAAA,gBAAiBiB,MAAM,qBAAvBjB,yBAAyBoB,aAAa;YAEzE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAACC,iBAAiB,CAACrP,KAAKE;YAE5B,IAAIgE,WAAW,MAAM,IAAI,CAACnE,gBAAgB,CAACC,KAAK2B,KAAKzB;YACrD,IAAIgE,UAAU;YAEd,MAAMf,gBAAe,qBAAA,IAAI,CAACJ,YAAY,qBAAjB,mBAAmBK,kBAAkB,CACxDkM,IAAAA,wBAAW,EAACpP,WAAWF,IAAIW,OAAO;YAGpC,MAAM0C,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,OAAI,wBAAA,IAAI,CAAC7D,UAAU,CAACqG,IAAI,qBAApB,sBAAsBxC,aAAa;YACpErC,IAAAA,2BAAc,EAAChB,KAAK,iBAAiBqD;YAErC,MAAM/B,MAAMiO,IAAAA,kBAAY,EAACvP,IAAIsB,GAAG,CAACkO,OAAO,CAAC,QAAQ;YACjD,MAAMC,eAAeC,IAAAA,wCAAmB,EAACpO,IAAInB,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3BuD,cAAc,IAAI,CAACA,YAAY;YACjC;YACAzB,IAAInB,QAAQ,GAAGsP,aAAatP,QAAQ;YAEpC,IAAIsP,aAAatH,QAAQ,EAAE;gBACzBnI,IAAIsB,GAAG,GAAGqO,IAAAA,kCAAgB,EAAC3P,IAAIsB,GAAG,EAAG,IAAI,CAAC9B,UAAU,CAAC2I,QAAQ;YAC/D;YAEA,MAAMyH,uBACJ,IAAI,CAAC7K,WAAW,IAAI,OAAO/E,IAAIW,OAAO,CAACkP,+BAAmB,CAAC,KAAK;YAElE,uCAAuC;YACvC,IAAID,sBAAsB;gBACxB,IAAI;wBAuBE,wBA2ByB,qBAkDjB;oBAnGZ,IAAI,IAAI,CAACzL,kBAAkB,CAACwC,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAI3G,IAAIsB,GAAG,CAAChB,KAAK,CAAC,mBAAmB;4BACnCN,IAAIsB,GAAG,GAAGtB,IAAIsB,GAAG,CAACkO,OAAO,CAAC,YAAY;wBACxC;wBACAtP,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAU2P,WAAW,EAAE,GAAG,IAAIC,IAClC/P,IAAIW,OAAO,CAACkP,+BAAmB,CAAC,EAChC;oBAGF,IAAI,EAAE1P,UAAU6P,WAAW,EAAE,GAAG,IAAID,IAAI/P,IAAIsB,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,KAAI,yBAAA,IAAI,CAAClB,WAAW,CAAC0D,IAAI,qBAArB,uBAAuBxD,KAAK,CAAC0P,cAAc;wBAC7ChP,IAAAA,2BAAc,EAAChB,KAAK,iBAAiB;oBACvC,OAGK,IACH,IAAI,CAAC0G,eAAe,IACpB,IAAI,CAAC3B,WAAW,IAChB/E,IAAIW,OAAO,CAACsP,8BAAkB,CAAC,KAAK,OACpCjQ,IAAIgM,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAM0C,OAAsB,EAAE;wBAC9B,WAAW,MAAMwB,SAASlQ,IAAI0O,IAAI,CAAE;4BAClCA,KAAK3K,IAAI,CAACmM;wBACZ;wBACA,MAAMC,YAAYC,OAAOC,MAAM,CAAC3B,MAAMS,QAAQ,CAAC;wBAE/CnO,IAAAA,2BAAc,EAAChB,KAAK,aAAamQ;oBACnC;oBAEAL,cAAc,IAAI,CAAC5O,SAAS,CAAC4O;oBAC7B,MAAMQ,oBAAoB,IAAI,CAACC,iBAAiB,CAACP;oBAEjD,8CAA8C;oBAC9C,MAAMQ,wBAAuB,sBAAA,IAAI,CAACzN,YAAY,qBAAjB,oBAAmBS,OAAO,CAACsM,aAAa;wBACnEzM;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAImN,sBAAsB;wBACxBxP,IAAAA,2BAAc,EAAChB,KAAK,UAAUwQ,qBAAqB/M,cAAc;wBAEjE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAI+M,qBAAqBC,mBAAmB,EAAE;4BAC5CzP,IAAAA,2BAAc,EAAChB,KAAK,6BAA6B;wBACnD,OAAO;4BACL0D,IAAAA,8BAAiB,EAAC1D,KAAK;wBACzB;oBACF;oBAEA,0CAA0C;oBAC1C8P,cAAcY,IAAAA,wCAAmB,EAACZ;oBAElC,IAAIa,cAAcb;oBAClB,IAAIc,gBAAgBC,IAAAA,sBAAc,EAACF;oBAEnC,IAAI,CAACC,eAAe;wBAClB,MAAMtQ,QAAQ,MAAM,IAAI,CAACoK,QAAQ,CAACpK,KAAK,CAACqQ,aAAa;4BACnD9K,MAAM2K;wBACR;wBAEA,6DAA6D;wBAC7D,IAAIlQ,OAAO;4BACTqQ,cAAcrQ,MAAMwQ,UAAU,CAAC3Q,QAAQ;4BACvC,iDAAiD;4BACjDyQ,gBAAgB,OAAOtQ,MAAMwB,MAAM,KAAK;wBAC1C;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAI0O,sBAAsB;wBACxBV,cAAcU,qBAAqBrQ,QAAQ;oBAC7C;oBAEA,MAAM4Q,QAAQC,IAAAA,qBAAQ,EAAC;wBACrBJ;wBACAK,MAAMN;wBACN9K,MAAM,IAAI,CAACrG,UAAU,CAACqG,IAAI;wBAC1BsC,UAAU,IAAI,CAAC3I,UAAU,CAAC2I,QAAQ;wBAClC+I,UAAU,EAAA,0BAAA,IAAI,CAACC,iBAAiB,uBAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAAC/R,UAAU,CAACC,YAAY,CAAC+R,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAInO,iBAAiB,CAACoM,aAAagC,MAAM,EAAE;wBACzCvR,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAEkD,gBAAgBnD,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,MAAMuR,wBAAwBxR,UAAUC,QAAQ;oBAChD,MAAMwR,mBAAmBvI,OAAOC,IAAI,CAClC0H,MAAMa,cAAc,CAAC5R,KAAKE;oBAE5B,MAAM2R,aAAaH,0BAA0BxR,UAAUC,QAAQ;oBAE/D,IAAI0R,cAAc3R,UAAUC,QAAQ,EAAE;wBACpCa,IAAAA,2BAAc,EAAChB,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBAEA,mEAAmE;oBACnE,mEAAmE;oBACnE,2CAA2C;oBAC3C,MAAM2R,cAAc;wBAAE,GAAG5R,UAAU0O,KAAK;oBAAC;oBAEzC,KAAK,MAAM,CAACmD,KAAKC,MAAM,IAAI5I,OAAO+C,OAAO,CAACjM,UAAU0O,KAAK,EAAG;wBAC1D,MAAMqD,gBAAgBC,IAAAA,+BAAuB,EAACH;wBAC9C,IAAI,CAACE,eAAe;wBAEpB,gEAAgE;wBAChE,+CAA+C;wBAC/C,OAAO/R,UAAU0O,KAAK,CAACmD,IAAI;wBAE3B,IAAI,OAAOC,UAAU,aAAa;wBAClCF,WAAW,CAACG,cAAc,GAAGD;oBAC/B;oBAEA,yDAAyD;oBACzD,IAAIpB,eAAe;wBACjB,IAAI9O,SAAiC,CAAC;wBAEtC,IAAIqQ,eAAepB,MAAMqB,2BAA2B,CAClDN,aACA;wBAGF,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAACK,aAAaE,cAAc,IAC5B,CAACxB,IAAAA,sBAAc,EAACP,oBAChB;4BACA,IAAIgC,gBAAgBvB,MAAMwB,mBAAmB,oBAAzBxB,MAAMwB,mBAAmB,MAAzBxB,OAA4BT;4BAEhD,IAAIgC,eAAe;gCACjBvB,MAAMqB,2BAA2B,CAACE,eAAe;gCACjDlJ,OAAOoJ,MAAM,CAACL,aAAarQ,MAAM,EAAEwQ;gCACnCH,aAAaE,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IACE,8DAA8D;wBAC9DvC,gBAAgB,YAChB,CAACqC,aAAaE,cAAc,IAC5B,CAACxB,IAAAA,sBAAc,EAACf,cAChB;4BACA,IAAIwC,gBAAgBvB,MAAMwB,mBAAmB,oBAAzBxB,MAAMwB,mBAAmB,MAAzBxB,OAA4BjB;4BAEhD,IAAIwC,eAAe;gCACjB,MAAMG,kBAAkB1B,MAAMqB,2BAA2B,CACvDE,eACA;gCAGF,IAAIG,gBAAgBJ,cAAc,EAAE;oCAClCjJ,OAAOoJ,MAAM,CAAC1Q,QAAQwQ;oCACtBH,eAAeM;gCACjB;4BACF;wBACF;wBAEA,IAAIN,aAAaE,cAAc,EAAE;4BAC/BvQ,SAASqQ,aAAarQ,MAAM;wBAC9B;wBAEA,MAAM4Q,qBAAqB1S,IAAIW,OAAO,CAAC,sBAAsB;wBAC7D,IACE,OAAO+R,uBAAuB,YAC9BA,sBACA7B,IAAAA,sBAAc,EAACf,gBACf,CAACqC,aAAaE,cAAc,EAC5B;4BACA,MAAMM,eACJ5B,MAAM6B,yBAAyB,CAACF;4BAElC,IAAIC,cAAc;gCAChBR,eAAepB,MAAMqB,2BAA2B,CAC9CO,cACA;gCAGF,IAAIR,aAAaE,cAAc,EAAE;oCAC/BvQ,SAASqQ,aAAarQ,MAAM;gCAC9B;4BACF;wBACF;wBAEA,mEAAmE;wBACnE,6DAA6D;wBAC7D,IAAI,CAACqQ,aAAaE,cAAc,EAAE;4BAChCF,eAAepB,MAAMqB,2BAA2B,CAC9CN,aACA;4BAGF,IAAIK,aAAaE,cAAc,EAAE;gCAC/BvQ,SAASqQ,aAAarQ,MAAM;4BAC9B;wBACF;wBAEA,uDAAuD;wBACvD,IACEiP,MAAM8B,mBAAmB,IACzBvC,sBAAsBK,eACtB,CAACwB,aAAaE,cAAc,IAC5B,CAACtB,MAAMqB,2BAA2B,CAAC;4BAAE,GAAGtQ,MAAM;wBAAC,GAAG,MAC/CuQ,cAAc,EACjB;4BACAvQ,SAASiP,MAAM8B,mBAAmB;4BAElC,8DAA8D;4BAC9D,kBAAkB;4BAClB7R,IAAAA,2BAAc,EAAChB,KAAK,6BAA6B;wBACnD;wBAEA,IAAI8B,QAAQ;4BACVgO,cAAciB,MAAM+B,sBAAsB,CAACnC,aAAa7O;4BACxD9B,IAAIsB,GAAG,GAAGyP,MAAM+B,sBAAsB,CAAC9S,IAAIsB,GAAG,EAAGQ;4BAEjD,kEAAkE;4BAClE,4DAA4D;4BAC5D,UAAU;4BACV,IAAIT,4BAA4BgB,IAAAA,2BAAc,EAC5CrC,KACA;4BAEF,IACEqB,6BACAwP,IAAAA,sBAAc,EAACxP,2BAA2B,QAC1C;gCACAA,4BAA4B0P,MAAM+B,sBAAsB,CACtDzR,2BACAS;gCAGF9B,IAAIW,OAAO,CAACI,qDAAmC,CAACF,WAAW,GAAG,GAC5DQ;gCACFL,IAAAA,2BAAc,EACZhB,KACA,6BACAqB;4BAEJ;wBACF;oBACF;oBAEA,IAAIuP,iBAAiBiB,YAAY;4BAGdd;wBAFjBA,MAAMgC,kBAAkB,CAAC/S,KAAK;+BACzB2R;+BACAvI,OAAOC,IAAI,CAAC0H,EAAAA,2BAAAA,MAAMiC,iBAAiB,qBAAvBjC,yBAAyBkC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACA/S,UAAUC,QAAQ,GAAG2P;oBACrBxO,IAAInB,QAAQ,GAAGD,UAAUC,QAAQ;oBACjC+D,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACjE,KAAK2B,KAAKzB;oBAC3D,IAAIgE,UAAU;gBAChB,EAAE,OAAO0H,KAAK;oBACZ,IAAIA,eAAesH,kBAAW,IAAItH,eAAeuH,qBAAc,EAAE;wBAC/DxR,IAAI+L,UAAU,GAAG;wBACjB,OAAO,IAAI,CAAC0F,WAAW,CAAC,MAAMpT,KAAK2B,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAMiK;gBACR;YACF;YAEA5K,IAAAA,2BAAc,EAAChB,KAAK,kBAAkBqT,QAAQlQ;YAE9C,IAAIsM,aAAagC,MAAM,EAAE;gBACvBzR,IAAIsB,GAAG,GAAGG,IAAAA,WAAS,EAACH;gBACpBN,IAAAA,2BAAc,EAAChB,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAAC+E,WAAW,IAAI,CAAC1C,IAAAA,2BAAc,EAACrC,KAAK,WAAW;gBACvD,gEAAgE;gBAChE,IAAIyP,aAAagC,MAAM,EAAE;oBACvBzQ,IAAAA,2BAAc,EAAChB,KAAK,UAAUyP,aAAagC,MAAM;gBACnD,OAGK,IAAIpO,eAAe;oBACtBrC,IAAAA,2BAAc,EAAChB,KAAK,UAAUqD;oBAC9BrC,IAAAA,2BAAc,EAAChB,KAAK,6BAA6B;gBACnD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAC,AAAC,IAAI,CAACkF,aAAa,CAASoO,eAAe,IAC5C,CAACjR,IAAAA,2BAAc,EAACrC,KAAK,qBACrB;gBACA,IAAIuT,WAA+B;gBAEnC,IAAI;oBACF,MAAMC,gBAAgB,IAAIzD,IACxB1N,IAAAA,2BAAc,EAACrC,KAAK,cAAc,KAClC;oBAEFuT,WAAWC,cAAcD,QAAQ;gBACnC,EAAE,OAAM,CAAC;gBAET,MAAME,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgBvK,OAAOoJ,MAAM,CAAC,CAAC,GAAGxS,IAAIW,OAAO;oBAC7CiT,iBAAiBL,SAASzQ,SAAS,CAAC,GAAGyQ,SAAS9Q,MAAM,GAAG;gBAG3D;gBAEAgR,iBAAiBI,iBAAiB;gBAClC7S,IAAAA,2BAAc,EAAChB,KAAK,oBAAoByT;gBACtC9T,WAAmBmU,kBAAkB,GAAGL;YAC5C;YAEA,kEAAkE;YAClE,kBAAkB;YAClB,MAAMM,WAAWC,IAAAA,0BAAgB;YACjC,IAAID,UAAU;gBACZ,MAAME,SAASjU,IAAIW,OAAO,CAACuT,8CAAkC,CAAC;gBAC9D,MAAMC,cAAc,OAAOF,WAAW,WAAWA,OAAO/Q,KAAK,CAAC,OAAO,EAAE;gBAEvE,MAAMkR,WAA4B,EAAE;gBACpC,KAAK,MAAMC,WAAWN,SAAU;oBAC9BK,SAASrQ,IAAI,CAACsQ,QAAQC,kBAAkB,IAAIH;gBAC9C;gBAEA,oDAAoD;gBACpD,IAAIC,SAAS3R,MAAM,GAAG,GAAG,MAAM8R,QAAQC,GAAG,CAACJ;YAC7C;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAAC/R,IAAAA,2BAAc,EAACrC,KAAK,6BAA6B;gBACpDgB,IAAAA,2BAAc,EACZhB,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAMkV,aAAapS,IAAAA,2BAAc,EAACrC,KAAK;YACvC,MAAM0U,gBACJ,CAAC9E,wBACD1N,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BqS;YAEF,IAAIC,eAAe;oBAkCf;gBAjCF,MAAMC,eAAetS,IAAAA,2BAAc,EAACrC,KAAK;gBACzC,IAAI2U,cAAc;oBAChB,MAAMC,cAAcvS,IAAAA,2BAAc,EAACrC,KAAK;oBAExC,IAAI4U,aAAa;wBACfxL,OAAOoJ,MAAM,CAACtS,UAAU0O,KAAK,EAAEgG;oBACjC;oBAEAjT,IAAI+L,UAAU,GAAGiH;oBACjB,IAAI/I,MAAoBvJ,IAAAA,2BAAc,EAACrC,KAAK,kBAAkB;oBAE9D,OAAO,IAAI,CAACoT,WAAW,CAACxH,KAAK5L,KAAK2B,KAAK,WAAWzB,UAAU0O,KAAK;gBACnE;gBAEA,MAAMiG,oBAAoB,IAAI9E,IAAI0E,cAAc,KAAK;gBACrD,MAAMK,qBAAqBpF,IAAAA,wCAAmB,EAC5CmF,kBAAkB1U,QAAQ,EAC1B;oBACEX,YAAY,IAAI,CAACA,UAAU;oBAC3BuV,WAAW;gBACb;gBAGF,IAAID,mBAAmBrD,MAAM,EAAE;oBAC7BzQ,IAAAA,2BAAc,EAAChB,KAAK,UAAU8U,mBAAmBrD,MAAM;gBACzD;gBAEA,IAAIvR,UAAUC,QAAQ,KAAK0U,kBAAkB1U,QAAQ,EAAE;oBACrDD,UAAUC,QAAQ,GAAG0U,kBAAkB1U,QAAQ;oBAC/Ca,IAAAA,2BAAc,EAAChB,KAAK,cAAc8U,mBAAmB3U,QAAQ;gBAC/D;gBACA,MAAM6U,kBAAkBC,IAAAA,wCAAmB,EACzCtF,IAAAA,kCAAgB,EAACzP,UAAUC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAAC2I,QAAQ,IAAI,MACjE,yBAAA,IAAI,CAAC3I,UAAU,CAACqG,IAAI,qBAApB,uBAAsBC,OAAO;gBAG/B,IAAIkP,gBAAgBvR,cAAc,EAAE;oBAClCzC,IAAAA,2BAAc,EAAChB,KAAK,UAAUgV,gBAAgBvR,cAAc;gBAC9D;gBACAvD,UAAUC,QAAQ,GAAG6U,gBAAgB7U,QAAQ;gBAE7C,KAAK,MAAM4R,OAAO3I,OAAOC,IAAI,CAACnJ,UAAU0O,KAAK,EAAG;oBAC9C,OAAO1O,UAAU0O,KAAK,CAACmD,IAAI;gBAC7B;gBACA,MAAM6C,cAAcvS,IAAAA,2BAAc,EAACrC,KAAK;gBAExC,IAAI4U,aAAa;oBACfxL,OAAOoJ,MAAM,CAACtS,UAAU0O,KAAK,EAAEgG;gBACjC;gBAEA1Q,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACjE,KAAK2B,KAAKzB;gBAC3D,IAAIgE,UAAU;gBAEd,MAAM,IAAI,CAACN,2BAA2B,CAAC5D,KAAK2B,KAAKzB;gBACjD;YACF;YAEA,IACEgC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,2BAAc,EAACrC,KAAK,qBACpB;gBACAkE,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACjE,KAAK2B,KAAKzB;gBAC3D,IAAIgE,UAAU;gBAEdA,WAAW,MAAM,IAAI,CAACL,+BAA+B,CACnD7D,KACA2B,KACAzB;gBAEF,IAAIgE,UAAU;gBAEd,MAAM0H,MAAM,IAAIxM;gBACdwM,IAAYrL,MAAM,GAAG;oBACrB2U,UAAU,IAAIC,SAAS,MAAM;wBAC3BxU,SAAS;4BACP,qBAAqB;wBACvB;oBACF;gBACF;gBACEiL,IAAYwJ,MAAM,GAAG;gBACvB,MAAMxJ;YACR;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAACgE,wBAAwBH,aAAatH,QAAQ,EAAE;gBAClDjI,UAAUC,QAAQ,GAAGwP,IAAAA,kCAAgB,EACnCzP,UAAUC,QAAQ,EAClBsP,aAAatH,QAAQ;YAEzB;YAEAxG,IAAI+L,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAAC2H,GAAG,CAACrV,KAAK2B,KAAKzB;QAClC,EAAE,OAAO0L,KAAU;YACjB,IAAIA,eAAe3M,iBAAiB;gBAClC,MAAM2M;YACR;YAEA,IACE,AAACA,OAAO,OAAOA,QAAQ,YAAYA,IAAI0J,IAAI,KAAK,qBAChD1J,eAAesH,kBAAW,IAC1BtH,eAAeuH,qBAAc,EAC7B;gBACAxR,IAAI+L,UAAU,GAAG;gBACjB,OAAO,IAAI,CAAC0F,WAAW,CAAC,MAAMpT,KAAK2B,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAACoD,WAAW,IAChB,IAAI,CAACyC,UAAU,CAAC1C,GAAG,IAClByQ,IAAAA,sBAAc,EAAC3J,QAAQA,IAAIwJ,MAAM,EAClC;gBACA,MAAMxJ;YACR;YACA,IAAI,CAACW,QAAQ,CAACiJ,IAAAA,uBAAc,EAAC5J;YAC7BjK,IAAI+L,UAAU,GAAG;YACjB/L,IAAI+M,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAwDA;;GAEC,GACD,AAAO8G,8BACLC,IAAiB,EACkC;QACnD,MAAMrB,UAAU,IAAI,CAACsB,iBAAiB;QACtC,OAAO,CAAC3V,KAAK2B,KAAKzB;YAChB0V,IAAAA,2BAAc,EAAC5V,KAAK0V;YACpB,OAAOrB,QAAQrU,KAAK2B,KAAKzB;QAC3B;IACF;IAEOyV,oBAGL;QACA,OAAO,IAAI,CAACnJ,aAAa,CAACzC,IAAI,CAAC,IAAI;IACrC;IAQOc,eAAegL,MAAe,EAAQ;QAC3C,IAAI,CAACrO,UAAU,CAACpB,WAAW,GAAGyP,SAASA,OAAOrG,OAAO,CAAC,OAAO,MAAM;IACrE;IAIA;;;GAGC,GACD,MAAa/C,UAAyB;QACpC,IAAI,IAAI,CAACpI,QAAQ,EAAE;QAEnB,IAAI,IAAI,CAACC,eAAe,KAAK,MAAM;YACjC,6BAA6B;YAC7B,IAAI,CAACwH,eAAe,GAAG,MAAM,IAAI,CAACgK,yBAAyB;YAC3D,IAAI,CAACxR,eAAe,GAAG,IAAI,CAACyR,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAAC3R,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgByR,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3B1L,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDlB,OAAOC,IAAI,CAAC,IAAI,CAACe,gBAAgB,IAAI,CAAC,GAAG8L,OAAO,CAAC,CAACC;YAChD,MAAMC,iBAAiBC,IAAAA,0BAAgB,EAACF;YACxC,IAAI,CAAC7L,aAAa,CAAC8L,eAAe,EAAE;gBAClC9L,aAAa,CAAC8L,eAAe,GAAG,EAAE;YACpC;YACA9L,aAAa,CAAC8L,eAAe,CAACrS,IAAI,CAACoS;QACrC;QACA,OAAO7L;IACT;IAEA,MAAgB+K,IACdrV,GAAkB,EAClB2B,GAAmB,EACnBzB,SAA6B,EACd;QACf,OAAO0M,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACsI,GAAG,EAAE,UAC3C,IAAI,CAACiB,OAAO,CAACtW,KAAK2B,KAAKzB;IAE3B;IAEA,MAAcoW,QACZtW,GAAkB,EAClB2B,GAAmB,EACnBzB,SAA6B,EACd;QACf,MAAM,IAAI,CAAC0D,2BAA2B,CAAC5D,KAAK2B,KAAKzB;IACnD;IAEA,MAAcqW,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAO7J,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACwJ,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,KAAKF,eAAezW,GAAG,CAACW,OAAO,CAAC,aAAa,IAAI;QACvD,MAAMiW,eAAeC,IAAAA,YAAK,EAACF;QAE3B,MAAM9K,MAAqD;YACzD,GAAG4K,cAAc;YACjBjP,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB,CAACmP;gBAC1BE,SAASC,IAAAA,iBAAU,EAACJ;gBACpBK,wBAAwBC,IAAAA,+CAA4B,EAClDN,IACA,IAAI,CAACnX,UAAU,CAAC+J,eAAe;YAEnC;QACF;QAEA,MAAM2N,UAAU,MAAMV,GAAG3K;QACzB,IAAIqL,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAElX,GAAG,EAAE2B,GAAG,EAAE,GAAGkK;QACrB,MAAMsL,iBAAiBxV,IAAI+L,UAAU;QACrC,MAAM,EAAEgB,IAAI,EAAE0I,IAAI,EAAE,GAAGF;QACvB,IAAI,EAAEG,YAAY,EAAE,GAAGH;QACvB,IAAI,CAACvV,IAAI2V,IAAI,EAAE;YACb,MAAM,EAAEjR,aAAa,EAAEsB,eAAe,EAAE7C,GAAG,EAAE,GAAG,IAAI,CAAC0C,UAAU;YAE/D,oDAAoD;YACpD,IAAI1C,KAAK;gBACPnD,IAAI4V,SAAS,CAAC,iBAAiB;gBAC/BF,eAAexX;YACjB;YAEA,IAAIwX,gBAAgBA,aAAaG,MAAM,KAAK3X,WAAW;gBACrDwX,aAAaG,MAAM,GAAG,IAAI,CAAChY,UAAU,CAACgK,UAAU;YAClD;YAEA,MAAM,IAAI,CAACiO,gBAAgB,CAACzX,KAAK2B,KAAK;gBACpCpB,QAAQmO;gBACR0I;gBACA/Q;gBACAsB;gBACA0P;YACF;YACA1V,IAAI+L,UAAU,GAAGyJ;QACnB;IACF;IAEA,MAAcO,cACZlB,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAM5K,MAAqD;YACzD,GAAG4K,cAAc;YACjBjP,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAMyP,UAAU,MAAMV,GAAG3K;QACzB,IAAIqL,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQxI,IAAI,CAACiJ,iBAAiB;IACvC;IAEA,MAAaC,OACX5X,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChByO,QAA4B,CAAC,CAAC,EAC9B1O,SAAkC,EAClC2X,iBAAiB,KAAK,EACP;QACf,OAAOjL,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC6K,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAAC9X,KAAK2B,KAAKxB,UAAUyO,OAAO1O,WAAW2X;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,wBAAwBC,IAAAA,+CAAwB;QACtD,IAAID,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBE,SAAS;QACxC;QAEA,IAAI,IAAI,CAACnT,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAOlF;QACT;QAEA,OAAO,IAAI,CAACsY,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAOtY;IACT;IAEA,MAAciY,WACZ9X,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChByO,QAA4B,CAAC,CAAC,EAC9B1O,SAAkC,EAClC2X,iBAAiB,KAAK,EACP;YAyBZ7X;QAxBH,IAAI,CAACG,SAASiY,UAAU,CAAC,MAAM;YAC7B/L,QAAQ3H,IAAI,CACV,CAAC,8BAA8B,EAAEvE,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAAC+E,aAAa,CAACmT,YAAY,IAC/BlY,aAAa,YACb,CAAE,MAAM,IAAI,CAACmY,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxCnY,WAAW;QACb;QAEA,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAAC0X,kBACD,CAAC,IAAI,CAAC9S,WAAW,IACjB,CAAC1C,IAAAA,2BAAc,EAACrC,KAAK,oBACpBA,CAAAA,EAAAA,WAAAA,IAAIsB,GAAG,qBAAPtB,SAASM,KAAK,CAAC,kBACb,IAAI,CAACqF,YAAY,IAAI3F,IAAIsB,GAAG,CAAEhB,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAACkM,aAAa,CAACxM,KAAK2B,KAAKzB;QACtC;QAEA,IAAIqY,IAAAA,qBAAa,EAACpY,WAAW;YAC3B,OAAO,IAAI,CAACmC,SAAS,CAACtC,KAAK2B,KAAKzB;QAClC;QAEA,OAAO,IAAI,CAACqW,IAAI,CAAC,CAAC1K,MAAQ,IAAI,CAAC2M,gBAAgB,CAAC3M,MAAM;YACpD7L;YACA2B;YACAxB;YACAyO;QACF;IACF;IAEA,MAAgB6J,eAAe,EAC7BtY,QAAQ,EAMT,EAGE;YAGC;QAFF,+DAA+D;QAC/D,MAAMuY,iBACJ,oDAAA,IAAI,CAAC3Q,oBAAoB,GAAG4Q,aAAa,CAACxY,SAAS,qBAAnD,kDAAqDmR,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvCsH,aAAa/Y;YACbgZ,cAAcC,IAAAA,4BAAkB,EAACJ;QACnC;IACF;IAEA,MAAcK,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,OAAOrM,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAACgM,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,OACEC,IAAAA,8CAA0B,EAACD,qBAC3B,IAAI,CAAC5O,yBAAyB,CAAC8O,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACJ;QACrB;IAEJ;IAEUK,cACRzZ,GAAkB,EAClB2B,GAAmB,EACnB+X,SAAkB,EAClBN,gBAAwB,EAClB;QACN,MAAMO,iBAAiB,GAAG/Y,4BAAU,CAAC,EAAE,EAAEgZ,+CAA6B,CAAC,EAAE,EAAE9Y,6CAA2B,CAAC,EAAE,EAAEC,qDAAmC,EAAE;QAChJ,MAAMyM,eAAenL,IAAAA,2BAAc,EAACrC,KAAK,mBAAmB;QAE5D,IAAI6Z,qBAAqB;QAEzB,IAAIH,aAAa,IAAI,CAACP,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FzX,IAAImY,YAAY,CAAC,QAAQ,GAAGH,eAAe,EAAE,EAAEI,0BAAQ,EAAE;YACzDF,qBAAqB;QACvB,OAAO,IAAIH,aAAalM,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnG7L,IAAImY,YAAY,CAAC,QAAQH;QAC3B;QAEA,IAAI,CAACE,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAO7Z,IAAIW,OAAO,CAACoZ,0BAAQ,CAAC;QAC9B;IACF;IAEA,MAAcb,mCACZ,EACElZ,GAAG,EACH2B,GAAG,EACHxB,QAAQ,EACRqH,YAAYwS,IAAI,EAC8B,EAChD,EAAEC,UAAU,EAAErL,KAAK,EAAwB,EACV;YAcJqL,uBAoHzB,uBA4CAC,OAiHY,wBAi4BdC,mBAmCAA;QAlsCF,IAAIha,aAAaia,qCAA0B,EAAE;YAC3Cja,WAAW;QACb;QACA,MAAMka,kBAAkBla,aAAa;QACrC,MAAMma,YACJna,aAAa,UAAWka,mBAAmB1Y,IAAI+L,UAAU,KAAK;QAChE,MAAM6M,YACJpa,aAAa,UAAWka,mBAAmB1Y,IAAI+L,UAAU,KAAK;QAChE,MAAMgM,YAAYO,WAAWP,SAAS,KAAK;QAE3C,MAAMc,iBAAiB,CAAC,CAACP,WAAWQ,kBAAkB;QACtD,IAAIC,oBAAoB,CAAC,CAACT,WAAWxB,cAAc;QACnD,MAAMkC,iBAAiBC,IAAAA,0CAAiB,EAAC5a;QACzC,MAAM6a,qBAAqB,CAAC,GAACZ,wBAAAA,WAAWa,SAAS,qBAApBb,sBAAsBc,eAAe;QAClE,IAAIC,QAAQ,CAAC,CAACf,WAAWgB,cAAc;QAEvC,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIjL,cAAcxO,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,IAAI,IAAInB,QAAQ,IAAI;QAEtD,IAAI+a,sBAAsB7Y,IAAAA,2BAAc,EAACrC,KAAK,iBAAiBgQ;QAE/D,IAAI,CAACyJ,aAAa,CAACzZ,KAAK2B,KAAK+X,WAAWwB;QAExC,IAAItC;QACJ,IAAIC;QACJ,IAAIsC,cAAc;QAElB,MAAMC,YAAYvK,IAAAA,sBAAc,EAACoJ,WAAWhJ,IAAI;QAEhD,MAAMiJ,oBAAoB,IAAI,CAACnS,oBAAoB;QAEnD,IAAI2R,aAAa0B,WAAW;YAC1B,MAAMC,cAAc,MAAM,IAAI,CAAC5C,cAAc,CAAC;gBAC5CtY;gBACA8Q,MAAMgJ,WAAWhJ,IAAI;gBACrByI;gBACA/F,gBAAgB3T,IAAIW,OAAO;YAC7B;YAEAiY,cAAcyC,YAAYzC,WAAW;YACrCC,eAAewC,YAAYxC,YAAY;YACvCsC,cAAc,OAAOtC,iBAAiB;YAEtC,IAAI,IAAI,CAACrZ,UAAU,CAAC+I,MAAM,KAAK,UAAU;gBACvC,MAAM0I,OAAOgJ,WAAWhJ,IAAI;gBAC5B,IAAI,CAAC2H,aAAa;oBAChB,MAAM,qBAEL,CAFK,IAAIxZ,MACR,CAAC,MAAM,EAAE6R,KAAK,wGAAwG,CAAC,GADnH,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMqK,uBAAuBC,IAAAA,wCAAmB,EAACL;gBACjD,IAAI,CAACtC,YAAY4C,QAAQ,CAACF,uBAAuB;oBAC/C,MAAM,qBAEL,CAFK,IAAIlc,MACR,CAAC,MAAM,EAAE6R,KAAK,oBAAoB,EAAEqK,qBAAqB,8EAA8E,CAAC,GADpI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAIH,aAAa;gBACfT,oBAAoB;YACtB;QACF;QAEA,IACES,gBACAvC,+BAAAA,YAAa4C,QAAQ,CAACN,yBACtB,mDAAmD;QACnD,+BAA+B;QAC/Blb,IAAIW,OAAO,CAAC,sBAAsB,EAClC;YACAqa,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAACxT,UAAU,CAAC1C,GAAG,EAAE;YAC/BkW,UAAU,CAAC,CAACd,kBAAkBuB,MAAM,CAACC,IAAAA,gBAAO,EAACvb,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAMwb,oBACJ,CAAC,CACCtZ,CAAAA,IAAAA,2BAAc,EAACrC,KAAK,oBACnBA,IAAIW,OAAO,CAAC,gBAAgB,IAC3B,AAAC,IAAI,CAACuE,aAAa,CAASoO,eAAe,KAE9C0H,CAAAA,SAASR,cAAa;QAEzB;;;KAGC,GACD,MAAMoB,uBACJvZ,IAAAA,2BAAc,EAACrC,KAAK,2BAA2B;QAEjD,uFAAuF;QAEvF,MAAMwN,eAAenL,IAAAA,2BAAc,EAACrC,KAAK,mBAAmB;QAE5D,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACgb,SACDhb,IAAIW,OAAO,CAAC,wBAAwB,IACpC,CAAE2Z,CAAAA,aAAana,aAAa,SAAQ,GACpC;YACAwB,IAAI4V,SAAS,CAAC1H,+BAAmB,EAAE1P;YACnCwB,IAAI4V,SAAS,CAAC,qBAAqB;YACnC5V,IAAI4V,SAAS,CACX,iBACA;YAEF5V,IAAI+M,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,iEAAiE;QACjE,IACEqM,SACA,IAAI,CAACjW,WAAW,IAChB/E,IAAIW,OAAO,CAACkP,+BAAmB,CAAC,IAChC7P,IAAIsB,GAAG,CAAC8W,UAAU,CAAC,gBACnB;YACApY,IAAIsB,GAAG,GAAG,IAAI,CAACiP,iBAAiB,CAACvQ,IAAIsB,GAAG;QAC1C;QAEA,MAAMmQ,SAASpP,IAAAA,2BAAc,EAACrC,KAAK;QACnC,MAAMqD,gBAAgB2X,SAClB,wBAAA,IAAI,CAACxb,UAAU,CAACqG,IAAI,qBAApB,sBAAsBxC,aAAa,GACnChB,IAAAA,2BAAc,EAACrC,KAAK;QAExB,IACE,CAAC,CAACA,IAAIW,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACgB,IAAI+L,UAAU,IAAI/L,IAAI+L,UAAU,KAAK,GAAE,GACzC;YACA/L,IAAI4V,SAAS,CACX,yBACA,GAAG9F,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKtR,UAAU;QAE9C;QAEA,IAAI0b;QACJ,IAAI5B,WAAW4B,WAAW,EAAE;YAC1BA,cAAc5B,WAAW4B,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAACpV,eAAe,IACpB,OAAOmV,gBAAgB,eACvBE,IAAAA,4BAAoB,EAACF;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAMG,2BACJ9Z,QAAQC,GAAG,CAAC8Z,0CAA0C,KAAK,OAC3D,OAAOrN,MAAMsN,aAAa,KAAK,eAC/BJ;QAEF,sEAAsE;QACtE,6CAA6C;QAC7C,MAAMK,6BACJH,4BAA4BpN,MAAMsN,aAAa,KAAK;QAEtD,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAME,oBACJN,mBACC,CAAA,EACC5B,QAAAA,kBAAkBuB,MAAM,CAACtb,SAAS,IAClC+Z,kBAAkBvB,aAAa,CAACxY,SAAS,qBAF1C,AACC+Z,MAECmC,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BL,4BACE,CAAA,IAAI,CAACxU,UAAU,CAAC1C,GAAG,KAAK,QACvB,IAAI,CAACG,qBAAqB,KAAK,IAAG,CAAE;QAE5C,MAAMqX,qBACJN,4BAA4BI;QAE9B,oEAAoE;QACpE,iEAAiE;QACjE,MAAMG,yBACJD,sBAAsB,IAAI,CAAC9U,UAAU,CAAC1C,GAAG,KAAK;QAEhD,MAAM0X,uBAAuBL,8BAA8BC;QAE3D,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMK,mBAAmBL,oBACrB/Z,IAAAA,2BAAc,EAACrC,KAAK,eACpBH;QAEJ,0EAA0E;QAC1E,wEAAwE;QACxE,0DAA0D;QAC1D,MAAM6c,sBACJN,qBAAqB5O,gBAAgB,CAACoO;QAExC,yEAAyE;QACzE,iEAAiE;QACjE,yEAAyE;QACzE,yEAAyE;QACzE,MAAMe,wBAAwBta,IAAAA,2BAAc,EAC1CrC,KACA;QAGF,MAAM4c,YAAYC,IAAAA,mCAAgB,EAAC7c;QACnC,IAAI4c,aAAaR,mBAAmB;YAClCpB,QAAQ;YACR,IAAI,CAACxT,UAAU,CAACwP,sBAAsB,GAAG;QAC3C;QAEA,gEAAgE;QAChE,IAAIsD,aAAa,CAACqB,qBAAqB,CAACnO,cAAc;YACpD7L,IAAI+L,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAIoP,8BAAmB,CAACtB,QAAQ,CAACrb,WAAW;YAC1CwB,IAAI+L,UAAU,GAAGqP,SAAS5c,SAAS6c,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,+CAA+C;QAC/C,CAACrC,kBACD,uCAAuC;QACvC,CAAC8B,oBACD,CAACnC,aACD,CAACC,aACDpa,aAAa,aACbH,IAAIgM,MAAM,KAAK,UACfhM,IAAIgM,MAAM,KAAK,SACd,CAAA,OAAOiO,WAAWa,SAAS,KAAK,YAAYE,KAAI,GACjD;YACArZ,IAAI+L,UAAU,GAAG;YACjB/L,IAAI4V,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtC5V,IAAI+M,IAAI,CAAC,sBAAsBC,IAAI;YACnC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAOsL,WAAWa,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACL1D,MAAM;gBACN,0DAA0D;gBAC1D1I,MAAMuO,qBAAY,CAACC,UAAU,CAACjD,WAAWa,SAAS;YACpD;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAASlM,SAAS,CAACA,MAAM/G,GAAG,EAAE,OAAO+G,MAAM/G,GAAG;QAElD,IAAImS,KAAKvS,uBAAuB,KAAK,MAAM;gBAIhCwS;YAHT,MAAMtD,KAAK3W,IAAIW,OAAO,CAAC,aAAa,IAAI;YACxC,MAAMiW,eAAeC,IAAAA,YAAK,EAACF;YAC3B,MAAMwG,sBACJ,SAAOlD,uBAAAA,WAAWmD,QAAQ,qBAAnBnD,qBAAqBc,eAAe,MAAK,cAChD,oFAAoF;YACpFsC,gCAAqB,IAAIpD,WAAWmD,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClDpD,KAAKvS,uBAAuB,GAC1B,CAACuT,SAAS,CAACpE,gBAAgB,CAAChI,MAAM/G,GAAG,IAAIsV;QAC7C;QAEA,2DAA2D;QAC3D,IAAI,CAACxB,qBAAqBjC,aAAaM,KAAKlV,GAAG,EAAE;YAC/CkV,KAAKvS,uBAAuB,GAAG;QACjC;QAEA,MAAM3B,WAAU,yBAAA,IAAI,CAACtG,UAAU,CAACqG,IAAI,qBAApB,uBAAsBC,OAAO;QAE7C,IAAIwX;QACJ,IAAIC,gBAAgB;QAEpB,IAAI/C,kBAAkBQ,SAAStB,WAAW;YACxC,8DAA8D;YAC9D,IAAIxX,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,MAAM,EAAEob,iBAAiB,EAAE,GACzBrY,QAAQ;gBACVmY,cAAcE,kBACZxd,KACA2B,KACA,IAAI,CAAC6F,UAAU,CAACM,YAAY,EAC5B,CAAC,CAAC,IAAI,CAACtI,UAAU,CAACC,YAAY,CAACge,kBAAkB;gBAEnDF,gBAAgBD,gBAAgB;YAClC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,gCAAgC;QAChC,IACE5D,aACA,CAACM,KAAKlV,GAAG,IACT,CAACyY,iBACDvC,SACAxN,gBACA,CAACkP,uBACA,CAAA,CAACgB,IAAAA,4BAAa,EAAC1D,KAAK2D,OAAO,KAC1B,AAAC,IAAI,CAACzY,aAAa,CAASoO,eAAe,AAAD,GAC5C;YACAlS,IAAAA,sCAAkB,EAACpB,IAAIW,OAAO;QAChC;QAEA,IAAI,EAAEid,oBAAoB,EAAEC,uBAAuB,EAAE,GACnDC,IAAAA,mCAAyB,EAAC9d,KAAK,IAAI,CAACwH,UAAU,CAACM,YAAY;QAE7D,IAAIkT,SAAS,IAAI,CAACjW,WAAW,IAAI/E,IAAIW,OAAO,CAACkP,+BAAmB,CAAC,EAAE;YACjE,uEAAuE;YACvEqL,sBAAsBlL;QACxB;QAEAA,cAAcuL,IAAAA,wCAAmB,EAACvL;QAClCkL,sBAAsBK,IAAAA,wCAAmB,EAACL;QAC1C,IAAI,IAAI,CAAClV,gBAAgB,EAAE;YACzBkV,sBAAsB,IAAI,CAAClV,gBAAgB,CAAC9E,SAAS,CAACga;QACxD;QAEA,MAAM6C,iBAAiB,CAACC;YACtB,MAAMvP,WAAW;gBACfwP,aAAaD,SAASE,SAAS,CAACC,YAAY;gBAC5CzQ,YAAYsQ,SAASE,SAAS,CAACE,mBAAmB;gBAClDjW,UAAU6V,SAASE,SAAS,CAACG,sBAAsB;YACrD;YACA,MAAM3Q,aAAa4Q,IAAAA,iCAAiB,EAAC7P;YACrC,MAAM,EAAEtG,QAAQ,EAAE,GAAG,IAAI,CAAC3I,UAAU;YAEpC,IACE2I,YACAsG,SAAStG,QAAQ,KAAK,SACtBsG,SAASwP,WAAW,CAAC7F,UAAU,CAAC,MAChC;gBACA3J,SAASwP,WAAW,GAAG,GAAG9V,WAAWsG,SAASwP,WAAW,EAAE;YAC7D;YAEA,IAAIxP,SAASwP,WAAW,CAAC7F,UAAU,CAAC,MAAM;gBACxC3J,SAASwP,WAAW,GAAGzP,IAAAA,+BAAwB,EAACC,SAASwP,WAAW;YACtE;YAEAtc,IACG8M,QAAQ,CAACA,SAASwP,WAAW,EAAEvQ,YAC/BgB,IAAI,CAACD,SAASwP,WAAW,EACzBtP,IAAI;QACT;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAIgN,mBAAmB;YACrBT,sBAAsB,IAAI,CAAC3K,iBAAiB,CAAC2K;YAC7ClL,cAAc,IAAI,CAACO,iBAAiB,CAACP;QACvC;QAEA,IAAIuO,cAA6B;QACjC,IACE,CAAChB,iBACDvC,SACA,CAAChB,KAAKvS,uBAAuB,IAC7B,CAACkT,kBACD,CAAC8B,oBACD,CAACC,qBACD;YACA6B,cAAc,GAAG9M,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACvC,AAACtR,CAAAA,aAAa,OAAO+a,wBAAwB,GAAE,KAAMzJ,SACjD,KACAyJ,sBACHtM,MAAM/G,GAAG,GAAG,SAAS,IAAI;QAC9B;QAEA,IAAI,AAACyS,CAAAA,aAAaC,SAAQ,KAAMS,OAAO;YACrCuD,cAAc,GAAG9M,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKtR,WAC5CyO,MAAM/G,GAAG,GAAG,SAAS,IACrB;QACJ;QAEA,IAAI0W,aAAa;YACfA,cAAcC,IAAAA,kCAAgB,EAACD;YAE/B,+CAA+C;YAC/CA,cACEA,gBAAgB,YAAYpe,aAAa,MAAM,MAAMoe;QACzD;QACA,IAAIhL,WAA+B;QAEnC,IAAI;YACF,MAAMC,gBAAgB,IAAIzD,IACxB1N,IAAAA,2BAAc,EAACrC,KAAK,cAAc,KAClC;YAEFuT,WAAWC,cAAcD,QAAQ;QACnC,EAAE,OAAM,CAAC;QAET,sDAAsD;QACtD,MAAME,mBACJ,AAAC9T,WAAmBmU,kBAAkB,IACrC,MAAM,IAAI,CAACJ,mBAAmB,CAAC;YAC9BC,gBAAgBvK,OAAOoJ,MAAM,CAAC,CAAC,GAAGxS,IAAIW,OAAO;YAC7CiT,iBAAiBL,SAASzQ,SAAS,CAAC,GAAGyQ,SAAS9Q,MAAM,GAAG;QAG3D;QAEF,0EAA0E;QAC1EgR,iBAAiBI,iBAAiB;QAoBlC,MAAM4K,WAAqB,OAAO,EAChCtO,SAAS,EACTuO,gBAAgB,KAAK,EACrBC,mBAAmB,EACpB;YACC,2DAA2D;YAC3D,IAAIlX,0BAGF,AAFA,uEAAuE;YACvE,6DAA6D;YAC5D,CAACkU,qBAAqB3B,KAAKlV,GAAG,KAAK,QACpC,qEAAqE;YACrE,gBAAgB;YACf,CAACkW,SAAS,CAACN,qBACZ,mEAAmE;YACnE,QAAQ;YACR,OAAOvK,cAAc,YACrB,sEAAsE;YACtE,uBAAuB;YACvBuM;YAEF,MAAMkC,YAAYpd,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,IAAI,IAAI,MAAMsN,KAAK;YAErD,mDAAmD;YACnD,kBAAkB;YAClB,IAAIoL,KAAKlY,MAAM,EAAE;gBACfsH,OAAOC,IAAI,CAAC2Q,KAAKlY,MAAM,EAAEoU,OAAO,CAAC,CAACnE;oBAChC,OAAO6M,SAAS,CAAC7M,IAAI;gBACvB;YACF;YACA,MAAM8M,mBACJ7O,gBAAgB,OAAO,IAAI,CAACxQ,UAAU,CAACqD,aAAa;YAEtD,MAAMic,cAAcrd,IAAAA,WAAS,EAAC;gBAC5BtB,UAAU,GAAG+a,sBAAsB2D,mBAAmB,MAAM,IAAI;gBAChE,uDAAuD;gBACvDjQ,OAAOgQ;YACT;YAEA,uEAAuE;YACvE,MAAMG,uBAAuBnC,aAAaR;YAE1C,MAAM5U,aAA+B;gBACnC,GAAGyS,UAAU;gBACb,GAAGD,IAAI;gBACP,GAAIN,YACA;oBACEjG;oBACA,gEAAgE;oBAChE,+DAA+D;oBAC/D,4DAA4D;oBAC5D,WAAW;oBACXuL,cAAchE,SAAS,CAAC7K,aAAa,CAACuM;oBACtCuC,eAAe,IAAI,CAACzf,UAAU,CAACC,YAAY,CAACwf,aAAa;gBAC3D,IACA,CAAC,CAAC;gBACNtD;gBACAmD;gBACArN;gBACA3L;gBACAzC;gBACAoa,oBAAoB,IAAI,CAACje,UAAU,CAACC,YAAY,CAACge,kBAAkB;gBACnE,uFAAuF;gBACvF,8DAA8D;gBAC9D,SAAS;gBACTyB,gBACE1E,kBAAkBK,qBACdpZ,IAAAA,WAAS,EAAC;oBACR,iEAAiE;oBACjE,UAAU;oBACVtB,UAAU,GAAG6P,cAAc6O,mBAAmB,MAAM,IAAI;oBACxDjQ,OAAOgQ;gBACT,KACAE;gBACNrf,cAAc;oBACZ,GAAGua,KAAKva,YAAY;oBACpB2c;gBACF;gBACA3U;gBACAsX;gBACAnB;gBACAuB,aAAa5B;gBACb5C;gBACAxK;gBACA+H,WAAW,IAAI,CAACH,YAAY;gBAC5BqH,SAASzd,IAAIyd,OAAO,CAACrV,IAAI,CAACpI;gBAC1B0d,kBAAkBxf;gBAClB,wBAAwB;gBACxByf,cAAc,AAAC,IAAI,CAASA,YAAY;YAC1C;YAEA,IAAIhD,sBAAsBC,wBAAwB;gBAChD9U,0BAA0B;gBAC1BD,WAAW+X,UAAU,GAAG;gBACxB/X,WAAWC,uBAAuB,GAAG;gBACrCD,WAAWgY,kBAAkB,GAAG;gBAChChY,WAAWwX,YAAY,GAAG;gBAC1BxX,WAAW+U,sBAAsB,GAAGA;YACtC;YAEA,qEAAqE;YACrE,wBAAwB;YACxB,IAAIhc;YAEJ,IAAIsb,aAAa;gBACf,IAAI4D,IAAAA,6BAAqB,EAAC5D,cAAc;wBAuBf;oBAtBvB,IACE,qEAAqE;oBACrE,6DAA6D;oBAC7D3Z,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC0M,IAAAA,0BAAiB,EAAC9O,QACnB,CAACmO,IAAAA,2BAAkB,EAACxM,MACpB;wBACA,MAAM,qBAEL,CAFK,IAAIvC,MACR,0EADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,MAAMsgB,UAAuC;wBAC3C5d,QAAQkY,KAAKlY,MAAM;wBACnBoY;wBACA1S,YAAY;4BACV/H,cAAc;gCACZiK,WAAWlC,WAAW/H,YAAY,CAACiK,SAAS;gCAC5CE,gBAAgBpC,WAAW/H,YAAY,CAACmK,cAAc;4BACxD;4BACAnC;4BACAgM;4BACA5K,iBAAiB,GAAE,gCAAA,IAAI,CAACrJ,UAAU,CAACC,YAAY,qBAA5B,8BAA8BqJ,SAAS;4BAC1DkW,cAAchE;4BACd9C,WAAW,IAAI,CAACH,YAAY;4BAC5BqH,SAASzd,IAAIyd,OAAO,CAACrV,IAAI,CAACpI;4BAC1B0d,kBAAkBxf;4BAClBgK,+BACE,IAAI,CAACrC,UAAU,CAACqC,6BAA6B;wBACjD;wBACA8V,eAAe;4BACb1d,SAAS,IAAI,CAACA,OAAO;wBACvB;oBACF;oBAEA,IAAI;wBACF,MAAM2d,UAAUC,+BAAkB,CAACC,mBAAmB,CACpD9f,KACA+f,IAAAA,mCAAsB,EAACpe,IAAIyM,gBAAgB;wBAG7C,MAAM8G,WAAW,MAAM2G,YAAYmE,MAAM,CAACJ,SAASF;wBAEjD1f,IAAYigB,YAAY,GAAG,AAC3BP,QAAQlY,UAAU,CAClByY,YAAY;wBAEd,MAAMC,YAAYR,QAAQlY,UAAU,CAAC2Y,aAAa;wBAElD,mEAAmE;wBACnE,oBAAoB;wBACpB,IAAInF,OAAO;4BACT,MAAMoF,OAAO,MAAMlL,SAASkL,IAAI;4BAEhC,sCAAsC;4BACtC,MAAMzf,UAAU0f,IAAAA,iCAAyB,EAACnL,SAASvU,OAAO;4BAE1D,IAAIuf,WAAW;gCACbvf,OAAO,CAAC2f,kCAAsB,CAAC,GAAGJ;4BACpC;4BAEA,IAAI,CAACvf,OAAO,CAAC,eAAe,IAAIyf,KAAKhJ,IAAI,EAAE;gCACzCzW,OAAO,CAAC,eAAe,GAAGyf,KAAKhJ,IAAI;4BACrC;4BAEA,MAAMmJ,aACJ,OAAOb,QAAQlY,UAAU,CAACgZ,mBAAmB,KAAK,eAClDd,QAAQlY,UAAU,CAACgZ,mBAAmB,IAAIC,0BAAc,GACpD,QACAf,QAAQlY,UAAU,CAACgZ,mBAAmB;4BAE5C,MAAMhJ,SACJ,OAAOkI,QAAQlY,UAAU,CAACkZ,eAAe,KAAK,eAC9ChB,QAAQlY,UAAU,CAACkZ,eAAe,IAAID,0BAAc,GAChD5gB,YACA6f,QAAQlY,UAAU,CAACkZ,eAAe;4BAExC,2CAA2C;4BAC3C,MAAMvG,aAAiC;gCACrCnI,OAAO;oCACL/E,MAAM0T,8BAAe,CAACC,SAAS;oCAC/BC,QAAQ3L,SAAS2L,MAAM;oCACvBnS,MAAM0B,OAAO0Q,IAAI,CAAC,MAAMV,KAAKW,WAAW;oCACxCpgB;gCACF;gCACA0W,cAAc;oCAAEkJ;oCAAY/I;gCAAO;gCACnCwJ,YAAY;4BACd;4BAEA,OAAO7G;wBACT;wBACA,IAAI8G,mBAAmBvB,QAAQlY,UAAU,CAACyZ,gBAAgB;wBAE1D,gDAAgD;wBAChD,qDAAqD;wBACrD,IAAIA,kBAAkB;4BACpB,IAAIvB,QAAQlY,UAAU,CAAC0Q,SAAS,EAAE;gCAChCwH,QAAQlY,UAAU,CAAC0Q,SAAS,CAAC+I;gCAC7BA,mBAAmBphB;4BACrB;wBACF;wBAEA,+DAA+D;wBAC/D,MAAMqhB,IAAAA,0BAAY,EAChBlhB,KACA2B,KACAuT,UACAwK,QAAQlY,UAAU,CAACyZ,gBAAgB;wBAErC,OAAO;oBACT,EAAE,OAAOrV,KAAK;wBACZ,MAAM,IAAI,CAAC9B,6BAA6B,CAAC8B,KAAK5L,KAAK;4BACjDmhB,YAAY;4BACZC,WAAWjhB;4BACXkhB,WAAW;4BACXC,kBAAkBC,IAAAA,2BAAmB,EAAC/Z;wBACxC;wBAEA,8DAA8D;wBAC9D,IAAIwT,OAAO,MAAMpP;wBAEjBnH,KAAI6H,KAAK,CAACV;wBAEV,kCAAkC;wBAClC,MAAMsV,IAAAA,0BAAY,EAAClhB,KAAK2B,KAAK,IAAIwT,SAAS,MAAM;4BAAE0L,QAAQ;wBAAI;wBAE9D,OAAO;oBACT;gBACF,OAAO,IACLW,IAAAA,0BAAkB,EAAC3F,gBACnBE,IAAAA,4BAAoB,EAACF,cACrB;oBACA,mDAAmD;oBACnD,IAAI7b,IAAIgM,MAAM,KAAK,aAAa,CAACsO,WAAW;wBAC1C,MAAM4G,IAAAA,0BAAY,EAAClhB,KAAK2B,KAAK,IAAIwT,SAAS,MAAM;4BAAE0L,QAAQ;wBAAI;wBAC9D,OAAO;oBACT;oBAEA,IAAIW,IAAAA,0BAAkB,EAAC3F,cAAc;wBACnC,wEAAwE;wBACxE,sEAAsE;wBACtE,iCAAiC;wBACjC,4HAA4H;wBAC5HrU,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBACnDI,WAAWia,uBAAuB,GAChCxH,WAAWwH,uBAAuB;wBAEpC,MAAM7B,UAAU9Q,IAAAA,0BAAiB,EAAC9O,OAAOA,IAAIgO,eAAe,GAAGhO;wBAC/D,MAAMkV,WAAW/G,IAAAA,2BAAkB,EAACxM,OAChCA,IAAIyM,gBAAgB,GACpBzM;wBAEJ,iDAAiD;wBACjD,IAAI;4BACFpB,SAAS,MAAMsb,YAAYjE,MAAM,CAC/BgI,SACA1K,UACA;gCACEjE,MAAM9Q;gCACN2B,QAAQkY,KAAKlY,MAAM;gCACnB8M;gCACApH;gCACAmY,eAAe;oCACb1d,SAAS,IAAI,CAACA,OAAO;oCACrBsF,cAAc,IAAI,CAAC/H,UAAU,CAAC+H,YAAY;oCAC1C8Q,cAAc,IAAI,CAACnT,aAAa,CAACmT,YAAY,IAAIxY;gCACnD;gCACA6hB,eAAe;oCACbV,YAAYtC;oCACZS,aAAa3X,WAAW2X,WAAW;oCACnCwC,+BAA+Btf,IAAAA,2BAAc,EAC3CrC,KACA;gCAEJ;4BACF;wBAEJ,EAAE,OAAO4L,KAAK;4BACZ,MAAM,IAAI,CAAC9B,6BAA6B,CAAC8B,KAAK5L,KAAK;gCACjDmhB,YAAY;gCACZC,WAAWjhB;gCACXkhB,WAAW;gCACXC,kBAAkBC,IAAAA,2BAAmB,EAAC;oCACpCvC,cAAchE;oCACd4C,sBAAsBpW,WAAWoW,oBAAoB;gCACvD;4BACF;4BACA,MAAMhS;wBACR;oBACF,OAAO;wBACL,MAAMgW,UAAS3H,WAAW4B,WAAW;wBAErC,4EAA4E;wBAC5E,8DAA8D;wBAC9D,4HAA4H;wBAC5HrU,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBAEnD,MAAMsY,UAAsC;4BAC1CzO,MAAMqJ,YAAY,SAASna;4BAC3B2B,QAAQkY,KAAKlY,MAAM;4BACnB8M;4BACA+P;4BACAnX;4BACA9H,0BAA0B,IAAI,CAACH,2BAA2B;4BAC1DogB,eAAe;gCACb1d,SAAS,IAAI,CAACA,OAAO;4BACvB;wBACF;wBAEA,4DAA4D;wBAC5D,iEAAiE;wBACjE,wCAAwC;wBACxC,IACE,IAAI,CAACzC,UAAU,CAACC,YAAY,CAACiK,SAAS,IACtC,IAAI,CAAClC,UAAU,CAAC1C,GAAG,IACnB,CAAC8W,wBACD,CAACjB,gBACD;4BACA,MAAMkH,SAAS,MAAMD,QAAOC,MAAM,CAAC7hB,KAAK2B,KAAK+d;4BAE7C,6DAA6D;4BAC7D,yBAAyB;4BACzB,IAAImC,OAAOC,QAAQ,CAACC,wBAAwB,EAAE;gCAC5Cva,WAAWua,wBAAwB,GACjCF,OAAOC,QAAQ,CAACC,wBAAwB;4BAC5C;wBACF;wBAEA,iDAAiD;wBACjDxhB,SAAS,MAAMqhB,QAAOhK,MAAM,CAAC5X,KAAK2B,KAAK+d;oBACzC;gBACF,OAAO;oBACL,MAAM,qBAAiD,CAAjD,IAAItgB,MAAM,yCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAgD;gBACxD;YACF,OAAO;gBACL,oEAAoE;gBACpE,iBAAiB;gBACjBmB,SAAS,MAAM,IAAI,CAACyhB,UAAU,CAAChiB,KAAK2B,KAAKxB,UAAUyO,OAAOpH;YAC5D;YAEA,MAAM,EAAEsa,QAAQ,EAAE,GAAGvhB;YAErB,MAAM,EACJ8W,YAAY,EACZ1W,UAAU,CAAC,CAAC,EACZ,oEAAoE;YACpEshB,WAAW/B,SAAS,EACrB,GAAG4B;YAEJ,IAAI5B,WAAW;gBACbvf,OAAO,CAAC2f,kCAAsB,CAAC,GAAGJ;YACpC;YAEA,2DAA2D;;YACzDlgB,IAAYigB,YAAY,GAAG6B,SAAS7B,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACEvG,aACAsB,SACA3D,CAAAA,gCAAAA,aAAckJ,UAAU,MAAK,KAC7B,CAAC,IAAI,CAAC/Y,UAAU,CAAC1C,GAAG,IACpB,CAACsX,mBACD;gBACA,MAAM8F,oBAAoBJ,SAASI,iBAAiB;gBAEpD,MAAMtW,MAAM,qBAOX,CAPW,IAAIxM,MACd,CAAC,+CAA+C,EAAE4Q,cAChDkS,CAAAA,qCAAAA,kBAAmBC,WAAW,IAC1B,CAAC,UAAU,EAAED,kBAAkBC,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAOZ;gBAEA,IAAID,qCAAAA,kBAAmBE,KAAK,EAAE;oBAC5B,MAAMA,QAAQF,kBAAkBE,KAAK;oBACrCxW,IAAIwW,KAAK,GAAGxW,IAAIyW,OAAO,GAAGD,MAAMtf,SAAS,CAACsf,MAAME,OAAO,CAAC;gBAC1D;gBAEA,MAAM1W;YACR;YAEA,uEAAuE;YACvE,iBAAiB;YAEjB,uBAAuB;YACvB,IAAI,gBAAgBkW,YAAYA,SAASS,UAAU,EAAE;gBACnD,OAAO;oBACLvQ,OAAO;oBACPqF;oBACA2J,YAAY;gBACd;YACF;YAEA,uBAAuB;YACvB,IAAIc,SAASU,UAAU,EAAE;gBACvB,OAAO;oBACLxQ,OAAO;wBACL/E,MAAM0T,8BAAe,CAAC8B,QAAQ;wBAC9BC,OAAOZ,SAAS9D,QAAQ,IAAI8D,SAASa,UAAU;oBACjD;oBACAtL;oBACA2J,YAAY;gBACd;YACF;YAEA,mBAAmB;YACnB,IAAIzgB,OAAOqiB,MAAM,EAAE;gBACjB,OAAO;YACT;YAEA,kEAAkE;YAClE,IAAIlJ,WAAW;gBACb,OAAO;oBACL1H,OAAO;wBACL/E,MAAM0T,8BAAe,CAACkC,QAAQ;wBAC9BC,MAAMviB;wBACNI;wBACAoiB,SAASjB,SAASa,UAAU;wBAC5BxS,WAAW2R,SAAS3R,SAAS;wBAC7B0Q,QAAQlf,IAAI+L,UAAU;wBACtBsV,aAAalB,SAASkB,WAAW;oBACnC;oBACA3L;oBACA2J,YAAY,CAAC,CAACrC;gBAChB;YACF;YAEA,OAAO;gBACL3M,OAAO;oBACL/E,MAAM0T,8BAAe,CAACsC,KAAK;oBAC3BH,MAAMviB;oBACNyd,UAAU8D,SAAS9D,QAAQ,IAAI8D,SAASa,UAAU;oBAClDhiB;oBACAkgB,QAAQnH,YAAY/X,IAAI+L,UAAU,GAAG7N;gBACvC;gBACAwX;gBACA2J,YAAYtC;YACd;QACF;QAEA,IAAIwE,oBAAuC,OAAO,EAChDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACf;YACC,MAAMC,eAAe,CAAC,IAAI,CAAC9b,UAAU,CAAC1C,GAAG;YACzC,MAAMye,aAAaJ,eAAexhB,IAAI2V,IAAI;YAE1C,sEAAsE;YACtE,IAAI,CAACsB,eAAewC,WAAW;gBAC7B,IAAIV,mBAAmB;oBACrB,MAAMW,cAAc,MAAM,IAAI,CAAC5C,cAAc,CAAC;wBAC5CtY;wBACAwT,gBAAgB3T,IAAIW,OAAO;wBAC3B+Y;wBACAzI,MAAMgJ,WAAWhJ,IAAI;oBACvB;oBAEA2H,cAAcyC,YAAYzC,WAAW;oBACrCC,eAAewC,YAAYxC,YAAY;gBACzC,OAAO;oBACLD,cAAc/Y;oBACdgZ,eAAe2K,sBAAY,CAACC,SAAS;gBACvC;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,IACE5K,iBAAiB2K,sBAAY,CAACE,SAAS,IACvC7M,IAAAA,YAAK,EAAC7W,IAAIW,OAAO,CAAC,aAAa,IAAI,KACnC;gBACAkY,eAAe2K,sBAAY,CAACG,sBAAsB;YACpD;YAEA,wDAAwD;YACxD,iCAAiC;YACjC,IACE/F,wBACAC,2BACA,CAACuF,sBACD,CAAC,IAAI,CAACre,WAAW,EACjB;gBACA,MAAM,IAAI,CAACzC,SAAS,CAACtC,KAAK2B;gBAC1B,OAAO;YACT;YAEA,IAAIyhB,CAAAA,sCAAAA,mBAAoBQ,OAAO,MAAK,CAAC,GAAG;gBACtChG,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACC/E,CAAAA,iBAAiB2K,sBAAY,CAACC,SAAS,IAAIL,kBAAiB,GAC7D;gBACAvK,eAAe2K,sBAAY,CAACG,sBAAsB;YACpD;YAEA,sEAAsE;YACtE,uDAAuD;YACvD,EAAE;YACF,sEAAsE;YACtE,8DAA8D;YAC9D,EAAE;YACF,sEAAsE;YACtE,0BAA0B;YAC1B,IAAIE,gBAAgBtF;YACpB,IAAI,CAACsF,iBAAiB7J,KAAKlV,GAAG,IAAI4U,WAAW;gBAC3CmK,gBAAgBrF,IAAAA,kCAAgB,EAACtD;YACnC;YACA,IAAI2I,iBAAiBjV,MAAM/G,GAAG,EAAE;gBAC9Bgc,gBAAgBA,cAAcrU,OAAO,CAAC,UAAU;YAClD;YAEA,MAAMsU,8BACJD,kBAAiBjL,+BAAAA,YAAa4C,QAAQ,CAACqI;YAEzC,qEAAqE;YACrE,kCAAkC;YAElC,kCAAkC;YAClC,IAAI,IAAI,CAACrkB,UAAU,CAACC,YAAY,CAAC6J,qBAAqB,EAAE;gBACtDuP,eAAe2K,sBAAY,CAACG,sBAAsB;YACpD;YAEA,oEAAoE;YACpE,kCAAkC;YAClC,EAAE;YACF,gCAAgC;YAChC,0CAA0C;YAC1C,wEAAwE;YACxE,iEAAiE;YACjE,yBAAyB;YACzB,iEAAiE;YACjE,qEAAqE;YACrE,EAAE;YACF,IACEzhB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC,IAAI,CAAC2C,WAAW,IACjB8T,iBAAiB2K,sBAAY,CAACG,sBAAsB,IACpDE,iBACA,CAACN,cACD,CAAChG,iBACDnC,aACCkI,CAAAA,gBAAgB,CAAC1K,eAAe,CAACkL,2BAA0B,GAC5D;gBACA,IAGE,AAFA,2DAA2D;gBAC3D,kBAAkB;gBACjBR,CAAAA,gBAAiB1K,eAAeA,CAAAA,+BAAAA,YAAanW,MAAM,IAAG,CAAC,KACxD,2DAA2D;gBAC3DoW,iBAAiB2K,sBAAY,CAACC,SAAS,EACvC;oBACA,MAAM,IAAIxkB;gBACZ;gBAEA,IAAI8kB;gBAEJ,kCAAkC;gBAClC,IAAIvC,IAAAA,0BAAkB,EAACvH,WAAW4B,WAAW,KAAK,CAACF,mBAAmB;oBACpE,gEAAgE;oBAChE,oCAAoC;oBACpCoI,mBAAmB,MAAM,IAAI,CAACjZ,aAAa,CAAC+C,GAAG,CAC7CyV,eAAgB7R,SAAS,CAAC,CAAC,EAAEA,SAAStR,UAAU,GAAGA,WAAY,MAC/D,yDAAyD;oBACzD,OAAO,EACLijB,oBAAoBY,6BAA6B,IAAI,EACtD;wBACC,2DAA2D;wBAC3D,8DAA8D;wBAC9D,gEAAgE;wBAChE,iEAAiE;wBACjE,YAAY;wBACZ,IAAIV,cAAc;4BAChB,OAAOW,IAAAA,4BAAoB,EAACD;wBAC9B;wBAEA,kEAAkE;wBAClE,UAAU;wBACV,OAAOvF,SAAS;4BACdtO,WAAWtQ;4BACX,2DAA2D;4BAC3D,+DAA+D;4BAC/D,qBAAqB;4BACrB6e,eAAe;4BACfC,qBAAqB;wBACvB;oBACF,GACA;wBACEuF,WAAWC,oBAAS,CAAClB,KAAK;wBAC1BxP;wBACA2I;wBACA4E,YAAY;oBACd;gBAEJ,OAGK,IACH5E,qBACAL,IAAAA,4BAAoB,EAAC9B,WAAW4B,WAAW,KAC3C,CAACrO,cACD;oBACA,gEAAgE;oBAChE,oCAAoC;oBACpCuW,mBAAmB,MAAM,IAAI,CAACjZ,aAAa,CAAC+C,GAAG,CAC7CyV,eAAenjB,WAAW,MAC1B,yDAAyD;oBACzD,UACEse,SAAS;4BACP,4DAA4D;4BAC5D,QAAQ;4BACRtO,WAAWtQ;4BACX6e,eAAe7e;4BACf8e,qBACE,yDAAyD;4BACzD,wDAAwD;4BACxD,YAAY;4BACZ2E,gBAAgB9G,uBACZ4H,IAAAA,sCAAsB,EAACjkB,YACvB;wBACR,IACF;wBACE+jB,WAAWC,oBAAS,CAACtB,QAAQ;wBAC7BpP;wBACA2I;wBACA4E,YAAY;oBACd;gBAEJ;gBAEA,wEAAwE;gBACxE,IAAI+C,qBAAqB,MAAM,OAAO;gBAEtC,qEAAqE;gBACrE,IAAIA,kBAAkB;oBACpB,sEAAsE;oBACtE,iCAAiC;oBACjC,OAAOA,iBAAiB1M,YAAY;oBAEpC,OAAO0M;gBACT;YACF;YAEA,wEAAwE;YACxE,oEAAoE;YACpE,MAAM5T,YACJ,CAACyN,wBAAwB,CAACyF,kBAAkB5G,mBACxCA,mBACA5c;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACE,AAACyc,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAOpM,cAAc,aACrB;gBACA,OAAO;oBACLkH,cAAc;wBAAEkJ,YAAY;wBAAG/I,QAAQ3X;oBAAU;oBACjDmhB,YAAY;oBACZhP,OAAO;wBACL/E,MAAM0T,8BAAe,CAACsC,KAAK;wBAC3BH,MAAM7F,qBAAY,CAACC,UAAU,CAAC;wBAC9Bc,UAAU,CAAC;wBACXrd,SAASd;wBACTghB,QAAQhhB;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAM8e,sBACJvD,aACAgB,qBACC/Z,CAAAA,IAAAA,2BAAc,EAACrC,KAAK,gCACnBwc,oBAAmB,IACjB4H,IAAAA,sCAAsB,EAACjkB,YACvB;YAEN,sBAAsB;YACtB,OAAOse,SAAS;gBACdtO;gBACAuO,eAAe7e;gBACf8e;YACF;QACF;QAEA,MAAMxE,aAAa,MAAM,IAAI,CAACrP,aAAa,CAAC+C,GAAG,CAC7C0Q,aACA2E,mBACA;YACEgB,WACE,sEAAsE;YACtE,qCAAqC;YACrCrI,CAAAA,+BAAAA,YAAa/K,UAAU,CAAC7D,IAAI,KAC3ByM,CAAAA,YAAYyK,oBAAS,CAACtB,QAAQ,GAAGsB,oBAAS,CAAClB,KAAK,AAAD;YAClDxP;YACAmK;YACAyG,YAAYrkB,IAAIW,OAAO,CAAC2jB,OAAO,KAAK;YACpClI;QACF;QAGF,IAAImB,eAAe;YACjB5b,IAAI4V,SAAS,CACX,iBACA;QAEJ;QAEA,IAAI,CAAC4C,YAAY;YACf,IAAIoE,eAAe,CAAEX,CAAAA,wBAAwBC,uBAAsB,GAAI;gBACrE,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,qBAA8D,CAA9D,IAAIze,MAAM,sDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;YACA,OAAO;QACT;QAEA,2EAA2E;QAC3E,4EAA4E;QAC5E,IACEmf,eACA,CAAC,IAAI,CAACxZ,WAAW,IACjBqX,qBACAjC,EAAAA,oBAAAA,WAAWnI,KAAK,qBAAhBmI,kBAAkBlN,IAAI,MAAK0T,8BAAe,CAACkC,QAAQ,IACnD1I,WAAW6G,UAAU,IACrB,CAACpD,wBACD,uEAAuE;QACvE,mBAAmB;QACnB,CAACpB,wBACDta,QAAQC,GAAG,CAACoiB,8BAA8B,KAAK,QAC/C;YACAC,IAAAA,6BAAkB,EAAC;gBACjB,IAAI;oBACF,MAAM,IAAI,CAAC1Z,aAAa,CAAC+C,GAAG,CAC1B0Q,aACA,IACEE,SAAS;4BACP,8DAA8D;4BAC9D,uBAAuB;4BACvBE,qBAAqB;4BACrBD,eAAe7e;4BACfsQ,WAAWtQ;wBACb,IACF;wBACEqkB,WAAWC,oBAAS,CAACtB,QAAQ;wBAC7BpP;wBACAmK,sBAAsB;wBACtByG,YAAY;wBACZjI,mBAAmB;oBACrB;gBAEJ,EAAE,OAAOxQ,KAAK;oBACZS,QAAQC,KAAK,CAAC,gDAAgDV;gBAChE;YACF;QACF;QAEA,MAAM6Y,cACJtK,EAAAA,qBAAAA,WAAWnI,KAAK,qBAAhBmI,mBAAkBlN,IAAI,MAAK0T,8BAAe,CAACkC,QAAQ,IACnD,OAAO1I,WAAWnI,KAAK,CAAC7B,SAAS,KAAK;QAExC,IACE6K,SACA,yEAAyE;QACzE,kEAAkE;QAClE,gDAAgD;QAChD,CAAC0B,uBACA,CAAA,CAAC+H,eAAe7I,oBAAmB,GACpC;YACA,IAAI,CAAC,IAAI,CAAC7W,WAAW,EAAE;gBACrB,gDAAgD;gBAChD,iCAAiC;gBACjCpD,IAAI4V,SAAS,CACX,kBACAqG,uBACI,gBACAzD,WAAWuK,MAAM,GACf,SACAvK,WAAWyJ,OAAO,GAChB,UACA;YAEZ;YACA,0EAA0E;YAC1E,yDAAyD;YACzDjiB,IAAI4V,SAAS,CAACoN,0CAAwB,EAAE;QAC1C;QAEA,MAAM,EAAE3S,OAAO4S,UAAU,EAAE,GAAGzK;QAE9B,IACE,OAAOwC,0BAA0B,YACjCiI,CAAAA,8BAAAA,WAAY3X,IAAI,MAAK0T,8BAAe,CAACkC,QAAQ,IAC7C+B,WAAW5B,WAAW,EACtB;YACA,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YAEtE,oEAAoE;YACpE,uEAAuE;YACvE,wEAAwE;YACxE,sEAAsE;YACtE,sEAAsE;YACtE,wDAAwD;YACxDrhB,IAAI4V,SAAS,CAACsN,0CAAwB,EAAE;YAExC,MAAMC,iBAAiBF,WAAW5B,WAAW,CAACnV,GAAG,CAAC8O;YAClD,IAAImI,mBAAmBjlB,WAAW;gBAChC,YAAY;gBACZ,OAAO;oBACLuX,MAAM;oBACN1I,MAAMuO,qBAAY,CAACC,UAAU,CAAC4H;oBAC9B,mEAAmE;oBACnE,+BAA+B;oBAC/BzN,cAAc8C,WAAW9C,YAAY;gBACvC;YACF;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,sEAAsE;YACtE,qEAAqE;YACrE,oEAAoE;YACpE,gCAAgC;YAChC1V,IAAI+L,UAAU,GAAG;YACjB,OAAO;gBACL0J,MAAM;gBACN1I,MAAMuO,qBAAY,CAACC,UAAU,CAAC;gBAC9B7F,YAAY,EAAE8C,8BAAAA,WAAY9C,YAAY;YACxC;QACF;QAEA,yDAAyD;QACzD,IAAIuN,CAAAA,8BAAAA,WAAY3X,IAAI,MAAK0T,8BAAe,CAACoE,KAAK,EAAE;YAC9C,MAAM,qBAAgE,CAAhE,IAAIC,8BAAc,CAAC,+CAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,sDAAsD;QACtD,IAAI3N;QAEJ,0EAA0E;QAC1E,oCAAoC;QACpC,IAAIoF,kBAAkB;YACpBpF,eAAe;gBAAEkJ,YAAY;gBAAG/I,QAAQ3X;YAAU;QACpD,OAKK,IACH,IAAI,CAACkF,WAAW,IAChByI,gBACA,CAACoO,wBACDQ,mBACA;YACA/E,eAAe;gBAAEkJ,YAAY;gBAAG/I,QAAQ3X;YAAU;QACpD,OAAO,IAAI,CAAC,IAAI,CAAC2H,UAAU,CAAC1C,GAAG,IAAK0V,kBAAkB,CAACmB,mBAAoB;YACzE,2DAA2D;YAC3D,IAAI4B,eAAe;gBACjBlG,eAAe;oBAAEkJ,YAAY;oBAAG/I,QAAQ3X;gBAAU;YACpD,OAIK,IAAI,CAACmb,OAAO;gBACf,IAAI,CAACrZ,IAAIsjB,SAAS,CAAC,kBAAkB;oBACnC5N,eAAe;wBAAEkJ,YAAY;wBAAG/I,QAAQ3X;oBAAU;gBACpD;YACF,OAQK,IAAIya,WAAW;gBAClB,MAAM4K,qBAAqB7iB,IAAAA,2BAAc,EAACrC,KAAK;gBAE/CqX,eAAe;oBACbkJ,YACE,OAAO2E,uBAAuB,cAAc,IAAIA;oBAClD1N,QAAQ3X;gBACV;YACF,OAAO,IAAI0a,WAAW;gBACpBlD,eAAe;oBAAEkJ,YAAY;oBAAG/I,QAAQ3X;gBAAU;YACpD,OAAO,IAAIsa,WAAW9C,YAAY,EAAE;gBAClC,wEAAwE;gBACxE,oBAAoB;gBACpB,IAAI,OAAO8C,WAAW9C,YAAY,CAACkJ,UAAU,KAAK,UAAU;wBAUtDpG;oBATJ,IAAIA,WAAW9C,YAAY,CAACkJ,UAAU,GAAG,GAAG;wBAC1C,MAAM,qBAEL,CAFK,IAAInhB,MACR,CAAC,2CAA2C,EAAE+a,WAAW9C,YAAY,CAACkJ,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAlJ,eAAe;wBACbkJ,YAAYpG,WAAW9C,YAAY,CAACkJ,UAAU;wBAC9C/I,QACE2C,EAAAA,2BAAAA,WAAW9C,YAAY,qBAAvB8C,yBAAyB3C,MAAM,KAAI,IAAI,CAAChY,UAAU,CAACgK,UAAU;oBACjE;gBACF,OAGK;oBACH6N,eAAe;wBAAEkJ,YAAY4E,0BAAc;wBAAE3N,QAAQ3X;oBAAU;gBACjE;YACF;QACF;QAEAsa,WAAW9C,YAAY,GAAGA;QAE1B,yEAAyE;QACzE,8BAA8B;QAC9B,MAAM+N,eAAe/iB,IAAAA,2BAAc,EAACrC,KAAK;QACzC,IAAIolB,cAAc;gBASRjL,oBAEIA;YAVZ,MAAMjW,WAAW,MAAMkhB,aACrB;gBACE,GAAGjL,UAAU;gBACb,0CAA0C;gBAC1C,wCAAwC;gBACxCnI,OAAO;oBACL,GAAGmI,WAAWnI,KAAK;oBACnB/E,MACEkN,EAAAA,qBAAAA,WAAWnI,KAAK,qBAAhBmI,mBAAkBlN,IAAI,MAAK0T,8BAAe,CAACkC,QAAQ,GAC/C,UACA1I,qBAAAA,WAAWnI,KAAK,qBAAhBmI,mBAAkBlN,IAAI;gBAC9B;YACF,GACA;gBACE3L,KAAKe,IAAAA,2BAAc,EAACrC,KAAK;YAC3B;YAEF,IAAIkE,UAAU;gBACZ,0CAA0C;gBAC1C,OAAO;YACT;QACF;QAEA,IAAI,CAAC0gB,YAAY;gBAQbzK;YAPF,oDAAoD;YACpD,qDAAqD;YACrD,4DAA4D;YAC5D,2BAA2B;YAC3BnZ,IAAAA,2BAAc,EACZhB,KACA,uBACAma,4BAAAA,WAAW9C,YAAY,qBAAvB8C,0BAAyBoG,UAAU;YAGrC,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAIpG,WAAW9C,YAAY,IAAI,CAAC1V,IAAIsjB,SAAS,CAAC,kBAAkB;gBAC9DtjB,IAAI4V,SAAS,CACX,iBACA8N,IAAAA,mCAAqB,EAAClL,WAAW9C,YAAY;YAEjD;YACA,IAAIsE,mBAAmB;gBACrBha,IAAI+L,UAAU,GAAG;gBACjB/L,IAAI+M,IAAI,CAAC,qBAAqBC,IAAI;gBAClC,OAAO;YACT;YAEA,IAAI,IAAI,CAACnH,UAAU,CAAC1C,GAAG,EAAE;gBACvB9D,IAAAA,2BAAc,EAAChB,KAAK,iCAAiCG;YACvD;YACA,MAAM,IAAI,CAACmC,SAAS,CAACtC,KAAK2B,KAAK;gBAAExB;gBAAUyO;YAAM,GAAG;YACpD,OAAO;QACT,OAAO,IAAIgW,WAAW3X,IAAI,KAAK0T,8BAAe,CAAC8B,QAAQ,EAAE;YACvD,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAItI,WAAW9C,YAAY,IAAI,CAAC1V,IAAIsjB,SAAS,CAAC,kBAAkB;gBAC9DtjB,IAAI4V,SAAS,CACX,iBACA8N,IAAAA,mCAAqB,EAAClL,WAAW9C,YAAY;YAEjD;YAEA,IAAIsE,mBAAmB;gBACrB,OAAO;oBACLvE,MAAM;oBACN1I,MAAMuO,qBAAY,CAACC,UAAU,CAC3B,6BAA6B;oBAC7BoI,KAAKC,SAAS,CAACX,WAAWlC,KAAK;oBAEjCrL,cAAc8C,WAAW9C,YAAY;gBACvC;YACF,OAAO;gBACL,MAAM0G,eAAe6G,WAAWlC,KAAK;gBACrC,OAAO;YACT;QACF,OAAO,IAAIkC,WAAW3X,IAAI,KAAK0T,8BAAe,CAACC,SAAS,EAAE;YACxD,MAAMjgB,UAAU6kB,IAAAA,mCAA2B,EAACZ,WAAWjkB,OAAO;YAE9D,IAAI,CAAE,CAAA,IAAI,CAACoE,WAAW,IAAIiW,KAAI,GAAI;gBAChCra,QAAQ8kB,MAAM,CAACnF,kCAAsB;YACvC;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IACEnG,WAAW9C,YAAY,IACvB,CAAC1V,IAAIsjB,SAAS,CAAC,oBACf,CAACtkB,QAAQkN,GAAG,CAAC,kBACb;gBACAlN,QAAQ+kB,GAAG,CACT,iBACAL,IAAAA,mCAAqB,EAAClL,WAAW9C,YAAY;YAEjD;YAEA,MAAM6J,IAAAA,0BAAY,EAChBlhB,KACA2B,KACA,IAAIwT,SAASyP,WAAWlW,IAAI,EAAE;gBAC5B/N;gBACAkgB,QAAQ+D,WAAW/D,MAAM,IAAI;YAC/B;YAEF,OAAO;QACT,OAAO,IAAI+D,WAAW3X,IAAI,KAAK0T,8BAAe,CAACkC,QAAQ,EAAE;gBAmCrD+B;YAlCF,oEAAoE;YACpE,gBAAgB;YAChB,IAAIH,eAAehI,kBAAkB;gBACnC,MAAM,qBAEL,CAFK,IAAIrd,MACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIwlB,WAAWjkB,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAGikB,WAAWjkB,OAAO;gBAAC;gBAExC,IAAI,CAAC,IAAI,CAACoE,WAAW,IAAI,CAACiW,OAAO;oBAC/B,OAAOra,OAAO,CAAC2f,kCAAsB,CAAC;gBACxC;gBAEA,KAAK,IAAI,CAACvO,KAAKC,MAAM,IAAI5I,OAAO+C,OAAO,CAACxL,SAAU;oBAChD,IAAI,OAAOqR,UAAU,aAAa;oBAElC,IAAI2T,MAAMC,OAAO,CAAC5T,QAAQ;wBACxB,KAAK,MAAM6T,KAAK7T,MAAO;4BACrBrQ,IAAImY,YAAY,CAAC/H,KAAK8T;wBACxB;oBACF,OAAO,IAAI,OAAO7T,UAAU,UAAU;wBACpCA,QAAQA,MAAM7C,QAAQ;wBACtBxN,IAAImY,YAAY,CAAC/H,KAAKC;oBACxB,OAAO;wBACLrQ,IAAImY,YAAY,CAAC/H,KAAKC;oBACxB;gBACF;YACF;YAEA,IACE,IAAI,CAACjN,WAAW,IAChBiW,WACA4J,sBAAAA,WAAWjkB,OAAO,qBAAlBikB,mBAAoB,CAACtE,kCAAsB,CAAC,GAC5C;gBACA3e,IAAI4V,SAAS,CACX+I,kCAAsB,EACtBsE,WAAWjkB,OAAO,CAAC2f,kCAAsB,CAAC;YAE9C;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAIsE,WAAW/D,MAAM,IAAK,CAAA,CAACrT,gBAAgB,CAAC4O,iBAAgB,GAAI;gBAC9Dza,IAAI+L,UAAU,GAAGkX,WAAW/D,MAAM;YACpC;YAEA,sCAAsC;YACtC,IAAI4D,aAAa;gBACf9iB,IAAI4V,SAAS,CAACsN,0CAAwB,EAAE;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAIrX,gBAAgB,CAAC+P,eAAe;gBAClC,8DAA8D;gBAC9D,IAAI,OAAOqH,WAAW7B,OAAO,KAAK,aAAa;oBAC7C,IAAI6B,WAAWzU,SAAS,EAAE;wBACxB,MAAM,qBAA0D,CAA1D,IAAI/Q,MAAM,kDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyD;oBACjE;oBAEA,OAAO;wBACLgY,MAAM;wBACN1I,MAAMkW,WAAW9B,IAAI;wBACrB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/EzL,cAAcqF,sBACV;4BAAE6D,YAAY;4BAAG/I,QAAQ3X;wBAAU,IACnCsa,WAAW9C,YAAY;oBAC7B;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO;oBACLD,MAAM;oBACN1I,MAAMuO,qBAAY,CAACC,UAAU,CAAC0H,WAAW7B,OAAO;oBAChD1L,cAAc8C,WAAW9C,YAAY;gBACvC;YACF;YAEA,mCAAmC;YACnC,IAAI3I,OAAOkW,WAAW9B,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAAC2B,eAAe,IAAI,CAAC1f,WAAW,EAAE;gBACpC,OAAO;oBACLqS,MAAM;oBACN1I;oBACA2I,cAAc8C,WAAW9C,YAAY;gBACvC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAIiF,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnD7N,KAAKoX,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,CAACC,yBAAY,CAACC,MAAM,CAACC,aAAa;wBACpDJ,WAAWhQ,KAAK;oBAClB;gBACF;gBAGF,OAAO;oBACLmB,MAAM;oBACN1I;oBACA2I,cAAc;wBAAEkJ,YAAY;wBAAG/I,QAAQ3X;oBAAU;gBACnD;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAMymB,cAAc,IAAIC;YACxB7X,KAAKoX,KAAK,CAACQ,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzE/H,SAAS;gBACPtO,WAAWyU,WAAWzU,SAAS;gBAC/BuO,eAAe7e;gBACf,sEAAsE;gBACtE,YAAY;gBACZ8e,qBAAqB;YACvB,GACG3I,IAAI,CAAC,OAAOzV;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,qBAAwD,CAAxD,IAAInB,MAAM,gDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuD;gBAC/D;gBAEA,IAAImB,EAAAA,gBAAAA,OAAOyR,KAAK,qBAAZzR,cAAc0M,IAAI,MAAK0T,8BAAe,CAACkC,QAAQ,EAAE;wBAELtiB;oBAD9C,MAAM,qBAEL,CAFK,IAAInB,MACR,CAAC,yCAAyC,GAAEmB,iBAAAA,OAAOyR,KAAK,qBAAZzR,eAAc0M,IAAI,EAAE,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,6CAA6C;gBAC7C,MAAM1M,OAAOyR,KAAK,CAAC8Q,IAAI,CAAC2D,MAAM,CAACH,YAAYI,QAAQ;YACrD,GACCC,KAAK,CAAC,CAAC/a;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1D0a,YAAYI,QAAQ,CAACE,KAAK,CAAChb,KAAK+a,KAAK,CAAC,CAACE;oBACrCxa,QAAQC,KAAK,CAAC,8BAA8Bua;gBAC9C;YACF;YAEF,OAAO;gBACLzP,MAAM;gBACN1I;gBACA,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrC2I,cAAc;oBAAEkJ,YAAY;oBAAG/I,QAAQ3X;gBAAU;YACnD;QACF,OAAO,IAAI8b,mBAAmB;YAC5B,OAAO;gBACLvE,MAAM;gBACN1I,MAAMuO,qBAAY,CAACC,UAAU,CAACoI,KAAKC,SAAS,CAACX,WAAW5G,QAAQ;gBAChE3G,cAAc8C,WAAW9C,YAAY;YACvC;QACF,OAAO;YACL,OAAO;gBACLD,MAAM;gBACN1I,MAAMkW,WAAW9B,IAAI;gBACrBzL,cAAc8C,WAAW9C,YAAY;YACvC;QACF;IACF;IAEQ9G,kBAAkBvO,IAAY,EAAE8kB,cAAc,IAAI,EAAE;QAC1D,IAAI9kB,KAAKwZ,QAAQ,CAAC,IAAI,CAACvZ,OAAO,GAAG;YAC/B,MAAM8kB,YAAY/kB,KAAKc,SAAS,CAC9Bd,KAAKsgB,OAAO,CAAC,IAAI,CAACrgB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACQ,MAAM;YAGlDT,OAAO0O,IAAAA,wCAAmB,EAACqW,UAAUvX,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAACxJ,gBAAgB,IAAI8gB,aAAa;YACxC,OAAO,IAAI,CAAC9gB,gBAAgB,CAAC9E,SAAS,CAACc;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChCglB,oBAAoBlZ,KAAa,EAAE;QAC3C,IAAI,IAAI,CAAC3J,kBAAkB,CAACwC,GAAG,EAAE;gBACP;YAAxB,MAAMsgB,mBAAkB,sBAAA,IAAI,CAAC3c,aAAa,qBAAlB,mBAAoB,CAACwD,MAAM;YAEnD,IAAI,CAACmZ,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACdrb,GAAkD,EAClDsb,gBAAyB,EACzB;YAkBgB;QAjBhB,MAAM,EAAEvY,KAAK,EAAEzO,QAAQ,EAAE,GAAG0L;QAE5B,MAAMub,WAAW,IAAI,CAACJ,mBAAmB,CAAC7mB;QAC1C,MAAMuZ,YAAYiM,MAAMC,OAAO,CAACwB;QAEhC,IAAInW,OAAO9Q;QACX,IAAIuZ,WAAW;YACb,4EAA4E;YAC5EzI,OAAOmW,QAAQ,CAACA,SAAS3kB,MAAM,GAAG,EAAE;QACtC;QAEA,MAAMlC,SAAS,MAAM,IAAI,CAAC8mB,kBAAkB,CAAC;YAC3C5V,QAAQpP,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;YAChCiR;YACArC;YACA9M,QAAQ+J,IAAIrE,UAAU,CAAC1F,MAAM,IAAI,CAAC;YAClC4X;YACA4N,YAAY,CAAC,GAAC,oCAAA,IAAI,CAAC9nB,UAAU,CAACC,YAAY,CAAC8nB,GAAG,qBAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAIlnB,QAAQ;YACVqM,IAAAA,iBAAS,IAAG8a,oBAAoB,CAAC,cAAcvnB;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC4Y,8BAA8B,CAAClN,KAAKtL;YACxD,EAAE,OAAOqL,KAAK;gBACZ,MAAM+b,oBAAoB/b,eAAe3M;gBAEzC,IAAI,CAAC0oB,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAMvb;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAc4M,iBACZ3M,GAAkD,EACjB;QACjC,OAAOe,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAACyL,gBAAgB,EAC/B;YACExL,UAAU,CAAC,cAAc,CAAC;YAC1BI,YAAY;gBACV,cAAcvB,IAAI1L,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAACynB,oBAAoB,CAAC/b;QACnC;IAEJ;IAQA,MAAc+b,qBACZ/b,GAAkD,EACjB;YAQzB;QAPR,MAAM,EAAE7L,GAAG,EAAE2B,GAAG,EAAEiN,KAAK,EAAEzO,QAAQ,EAAE,GAAG0L;QACtC,IAAIoF,OAAO9Q;QACX,MAAMgnB,mBACJ9kB,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE,uBAAuB;QACjD,OAAO4O,KAAK,CAACiZ,sCAAoB,CAAC;QAElC,MAAM/nB,UAAwB;YAC5B+F,IAAI,GAAE,qBAAA,IAAI,CAAC9C,YAAY,qBAAjB,mBAAmB+kB,WAAW,CAAC9nB,KAAKG;QAC5C;QAEA,IAAI;YACF,WAAW,MAAMG,SAAS,IAAI,CAACoK,QAAQ,CAACqd,QAAQ,CAAC5nB,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAMkoB,eAAe3lB,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAAC+E,WAAW,IACjB,OAAOijB,iBAAiB,YACxBnX,IAAAA,sBAAc,EAACmX,gBAAgB,OAC/BA,iBAAiB1nB,MAAMwQ,UAAU,CAAC3Q,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAMI,SAAS,MAAM,IAAI,CAAC2mB,mBAAmB,CAC3C;oBACE,GAAGrb,GAAG;oBACN1L,UAAUG,MAAMwQ,UAAU,CAAC3Q,QAAQ;oBACnCqH,YAAY;wBACV,GAAGqE,IAAIrE,UAAU;wBACjB1F,QAAQxB,MAAMwB,MAAM;oBACtB;gBACF,GACAqlB;gBAEF,IAAI5mB,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAAC2E,aAAa,CAACoO,eAAe,EAAE;gBACtC,sDAAsD;gBACtDzH,IAAI1L,QAAQ,GAAG,IAAI,CAAC+E,aAAa,CAACoO,eAAe,CAACrC,IAAI;gBACtD,MAAM1Q,SAAS,MAAM,IAAI,CAAC2mB,mBAAmB,CAACrb,KAAKsb;gBACnD,IAAI5mB,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAO+L,OAAO;YACd,MAAMV,MAAM4J,IAAAA,uBAAc,EAAClJ;YAE3B,IAAIA,iBAAiB2b,wBAAiB,EAAE;gBACtC5b,QAAQC,KAAK,CACX,yCACAgZ,KAAKC,SAAS,CACZ;oBACEtU;oBACA3P,KAAKuK,IAAI7L,GAAG,CAACsB,GAAG;oBAChBwO,aAAajE,IAAI7L,GAAG,CAACW,OAAO,CAACkP,+BAAmB,CAAC;oBACjDqY,SAAS7lB,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;oBACjC6R,YAAY,CAAC,CAACxP,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;oBACtCmoB,YAAY9lB,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAM4L;YACR;YAEA,IAAIA,eAAe3M,mBAAmBkoB,kBAAkB;gBACtD,MAAMvb;YACR;YACA,IAAIA,eAAesH,kBAAW,IAAItH,eAAeuH,qBAAc,EAAE;gBAC/DxR,IAAI+L,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAAC0a,qBAAqB,CAACvc,KAAKD;YAC/C;YAEAjK,IAAI+L,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAAC4K,OAAO,CAAC,SAAS;gBAC9BtX,IAAAA,2BAAc,EAAC6K,IAAI7L,GAAG,EAAE,qBAAqB;gBAC7C,MAAM,IAAI,CAACooB,qBAAqB,CAACvc,KAAKD;gBACtClI,IAAAA,8BAAiB,EAACmI,IAAI7L,GAAG,EAAE;YAC7B;YAEA,MAAMqoB,iBAAiBzc,eAAe1M;YAEtC,IAAI,CAACmpB,gBAAgB;gBACnB,IACE,AAAC,IAAI,CAACtjB,WAAW,IAAI7C,QAAQC,GAAG,CAACC,YAAY,KAAK,UAClD,IAAI,CAACoF,UAAU,CAAC1C,GAAG,EACnB;oBACA,IAAIwjB,IAAAA,gBAAO,EAAC1c,MAAMA,IAAIqF,IAAI,GAAGA;oBAC7B,MAAMrF;gBACR;gBACA,IAAI,CAACW,QAAQ,CAACiJ,IAAAA,uBAAc,EAAC5J;YAC/B;YACA,MAAMsJ,WAAW,MAAM,IAAI,CAACkT,qBAAqB,CAC/Cvc,KACAwc,iBAAiB,AAACzc,IAA0BtM,UAAU,GAAGsM;YAE3D,OAAOsJ;QACT;QAEA,MAAMtT,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IACED,cACA,CAAC,CAACiK,IAAI7L,GAAG,CAACW,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACgB,IAAI+L,UAAU,IAAI/L,IAAI+L,UAAU,KAAK,OAAO/L,IAAI+L,UAAU,KAAK,GAAE,GACnE;YACA,MAAM+D,SAASpP,IAAAA,2BAAc,EAACrC,KAAK;YAEnC2B,IAAI4V,SAAS,CACX,yBACA,GAAG9F,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKtR,UAAU;YAE5CwB,IAAI+L,UAAU,GAAG;YACjB/L,IAAI4V,SAAS,CAAC,gBAAgB;YAC9B5V,IAAI+M,IAAI,CAAC;YACT/M,IAAIgN,IAAI;YACR,OAAO;QACT;QAEAhN,IAAI+L,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC0a,qBAAqB,CAACvc,KAAK;IACzC;IAEA,MAAa0c,aACXvoB,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChByO,QAAwB,CAAC,CAAC,EACF;QACxB,OAAOhC,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACwb,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAACxoB,KAAK2B,KAAKxB,UAAUyO;QACnD;IACF;IAEA,MAAc4Z,iBACZxoB,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChByO,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC8I,aAAa,CAAC,CAAC7L,MAAQ,IAAI,CAAC2M,gBAAgB,CAAC3M,MAAM;YAC7D7L;YACA2B;YACAxB;YACAyO;QACF;IACF;IAEA,MAAawE,YACXxH,GAAiB,EACjB5L,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChByO,QAA4B,CAAC,CAAC,EAC9B6Z,aAAa,IAAI,EACF;QACf,OAAO7b,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACqG,WAAW,EAAE;YACnD,OAAO,IAAI,CAACsV,eAAe,CAAC9c,KAAK5L,KAAK2B,KAAKxB,UAAUyO,OAAO6Z;QAC9D;IACF;IAEA,MAAcC,gBACZ9c,GAAiB,EACjB5L,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChByO,QAA4B,CAAC,CAAC,EAC9B6Z,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACd9mB,IAAI4V,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAAChB,IAAI,CACd,OAAO1K;YACL,MAAMqJ,WAAW,MAAM,IAAI,CAACkT,qBAAqB,CAACvc,KAAKD;YACvD,IAAI,IAAI,CAAC7G,WAAW,IAAIpD,IAAI+L,UAAU,KAAK,KAAK;gBAC9C,MAAM9B;YACR;YACA,OAAOsJ;QACT,GACA;YAAElV;YAAK2B;YAAKxB;YAAUyO;QAAM;IAEhC;IAQA,MAAcwZ,sBACZvc,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOgB,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACqb,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACO,yBAAyB,CAAC9c,KAAKD;QAC7C;IACF;IAEA,MAAgB+c,0BACd9c,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAACpE,UAAU,CAAC1C,GAAG,IAAI+G,IAAI1L,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACLiX,MAAM;gBACN1I,MAAMuO,qBAAY,CAACC,UAAU,CAAC;YAChC;QACF;QACA,MAAM,EAAEvb,GAAG,EAAEiN,KAAK,EAAE,GAAG/C;QAEvB,IAAI;YACF,IAAItL,SAAsC;YAE1C,MAAMqoB,QAAQjnB,IAAI+L,UAAU,KAAK;YACjC,IAAImb,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAACzkB,kBAAkB,CAACwC,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CpG,SAAS,MAAM,IAAI,CAAC8mB,kBAAkB,CAAC;wBACrC5V,QAAQpP,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;wBAChCiR,MAAM6X,2CAAgC;wBACtCla;wBACA9M,QAAQ,CAAC;wBACT4X,WAAW;wBACX+N,cAAc;wBACdnmB,KAAKuK,IAAI7L,GAAG,CAACsB,GAAG;oBAClB;oBACAunB,eAAetoB,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAAC+X,OAAO,CAAC,SAAU;oBAC3C/X,SAAS,MAAM,IAAI,CAAC8mB,kBAAkB,CAAC;wBACrC5V,QAAQpP,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;wBAChCiR,MAAM;wBACNrC;wBACA9M,QAAQ,CAAC;wBACT4X,WAAW;wBACX,qEAAqE;wBACrE+N,cAAc;wBACdnmB,KAAKuK,IAAI7L,GAAG,CAACsB,GAAG;oBAClB;oBACAunB,eAAetoB,WAAW;gBAC5B;YACF;YACA,IAAIwoB,aAAa,CAAC,CAAC,EAAEpnB,IAAI+L,UAAU,EAAE;YAErC,IACE,CAACrL,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE,wBACzB,CAACO,UACDuc,8BAAmB,CAACtB,QAAQ,CAACuN,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAACvhB,UAAU,CAAC1C,GAAG,EAAE;oBACjDvE,SAAS,MAAM,IAAI,CAAC8mB,kBAAkB,CAAC;wBACrC5V,QAAQpP,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;wBAChCiR,MAAM8X;wBACNna;wBACA9M,QAAQ,CAAC;wBACT4X,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACT+N,cAAc;wBACdnmB,KAAKuK,IAAI7L,GAAG,CAACsB,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAACf,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAAC8mB,kBAAkB,CAAC;oBACrC5V,QAAQpP,IAAAA,2BAAc,EAACwJ,IAAI7L,GAAG,EAAE;oBAChCiR,MAAM;oBACNrC;oBACA9M,QAAQ,CAAC;oBACT4X,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACT+N,cAAc;oBACdnmB,KAAKuK,IAAI7L,GAAG,CAACsB,GAAG;gBAClB;gBACAynB,aAAa;YACf;YAEA,IACE7mB,QAAQC,GAAG,CAAC6mB,QAAQ,KAAK,gBACzB,CAACH,gBACA,MAAM,IAAI,CAACvQ,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAAC/T,oBAAoB;YAC3B;YAEA,IAAI,CAAChE,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAACiH,UAAU,CAAC1C,GAAG,EAAE;oBACvB,OAAO;wBACLsS,MAAM;wBACN,mDAAmD;wBACnD1I,MAAMuO,qBAAY,CAACC,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC;oBAEd;gBACF;gBAEA,MAAM,IAAIhe,kBACR,qBAA8C,CAA9C,IAAIE,MAAM,sCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YAEjD;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAImB,OAAO0Z,UAAU,CAAC4B,WAAW,EAAE;gBACjC7a,IAAAA,2BAAc,EAAC6K,IAAI7L,GAAG,EAAE,SAAS;oBAC/B8Q,YAAYvQ,OAAO0Z,UAAU,CAAC4B,WAAW,CAAC/K,UAAU;oBACpDhP,QAAQjC;gBACV;YACF,OAAO;gBACL6D,IAAAA,8BAAiB,EAACmI,IAAI7L,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC+Y,8BAA8B,CAC9C;oBACE,GAAGlN,GAAG;oBACN1L,UAAU4oB;oBACVvhB,YAAY;wBACV,GAAGqE,IAAIrE,UAAU;wBACjBoE;oBACF;gBACF,GACArL;YAEJ,EAAE,OAAO0oB,oBAAoB;gBAC3B,IAAIA,8BAA8BhqB,iBAAiB;oBACjD,MAAM,qBAAmD,CAAnD,IAAIG,MAAM,2CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAkD;gBAC1D;gBACA,MAAM6pB;YACR;QACF,EAAE,OAAO3c,OAAO;YACd,MAAM4c,oBAAoB1T,IAAAA,uBAAc,EAAClJ;YACzC,MAAM+b,iBAAiBa,6BAA6BhqB;YACpD,IAAI,CAACmpB,gBAAgB;gBACnB,IAAI,CAAC9b,QAAQ,CAAC2c;YAChB;YACAvnB,IAAI+L,UAAU,GAAG;YACjB,MAAMyb,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9Dvd,IAAI7L,GAAG,CAACsB,GAAG;YAGb,IAAI6nB,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;gBACnCnoB,IAAAA,2BAAc,EAAC6K,IAAI7L,GAAG,EAAE,SAAS;oBAC/B8Q,YAAYqY,mBAAmBtN,WAAW,CAAE/K,UAAU;oBACtDhP,QAAQjC;gBACV;gBAEA,OAAO,IAAI,CAACkZ,8BAA8B,CACxC;oBACE,GAAGlN,GAAG;oBACN1L,UAAU;oBACVqH,YAAY;wBACV,GAAGqE,IAAIrE,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtCoE,KAAKyc,iBACDa,kBAAkB5pB,UAAU,GAC5B4pB;oBACN;gBACF,GACA;oBACEta;oBACAqL,YAAYkP;gBACd;YAEJ;YACA,OAAO;gBACL/R,MAAM;gBACN1I,MAAMuO,qBAAY,CAACC,UAAU,CAAC;YAChC;QACF;IACF;IAEA,MAAamM,kBACXzd,GAAiB,EACjB5L,GAAkB,EAClB2B,GAAmB,EACnBxB,QAAgB,EAChByO,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC8I,aAAa,CAAC,CAAC7L,MAAQ,IAAI,CAACuc,qBAAqB,CAACvc,KAAKD,MAAM;YACvE5L;YACA2B;YACAxB;YACAyO;QACF;IACF;IAEA,MAAatM,UACXtC,GAAkB,EAClB2B,GAAmB,EACnBzB,SAA8D,EAC9DuoB,aAAa,IAAI,EACF;QACf,MAAM,EAAEtoB,QAAQ,EAAEyO,KAAK,EAAE,GAAG1O,YAAYA,YAAYsB,IAAAA,UAAQ,EAACxB,IAAIsB,GAAG,EAAG;QAEvE,uDAAuD;QACvD,IAAI,IAAI,CAAC9B,UAAU,CAACqG,IAAI,EAAE;YACxB,IAAI,CAACxD,IAAAA,2BAAc,EAACrC,KAAK,WAAW;gBAClCgB,IAAAA,2BAAc,EAAChB,KAAK,UAAU,IAAI,CAACR,UAAU,CAACqG,IAAI,CAACxC,aAAa;YAClE;YACArC,IAAAA,2BAAc,EAAChB,KAAK,iBAAiB,IAAI,CAACR,UAAU,CAACqG,IAAI,CAACxC,aAAa;QACzE;QAEA1B,IAAI+L,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC0F,WAAW,CAAC,MAAMpT,KAAK2B,KAAKxB,UAAWyO,OAAO6Z;IAC5D;AACF"}
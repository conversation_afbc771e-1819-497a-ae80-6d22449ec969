{"version": 3, "sources": ["../../src/server/font-utils.ts"], "sourcesContent": ["import {\n  DEFAULT_SERIF_FONT,\n  DEFAULT_SANS_SERIF_FONT,\n} from '../shared/lib/constants'\nconst capsizeFontsMetrics = require('next/dist/server/capsize-font-metrics.json')\n\nfunction formatName(str: string): string {\n  return str\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, function (word, index) {\n      return index === 0 ? word.toLowerCase() : word.toUpperCase()\n    })\n    .replace(/\\s+/g, '')\n}\n\nfunction formatOverrideValue(val: number) {\n  return Math.abs(val * 100).toFixed(2)\n}\n\nexport function calculateSizeAdjustValues(fontName: string) {\n  const fontKey = formatName(fontName)\n  const fontMetrics = capsizeFontsMetrics[fontKey]\n  let { category, ascent, descent, lineGap, unitsPerEm, xWidthAvg } =\n    fontMetrics\n  const mainFontAvgWidth = xWidthAvg / unitsPerEm\n  const fallbackFont =\n    category === 'serif' ? DEFAULT_SERIF_FONT : DEFAULT_SANS_SERIF_FONT\n  const fallbackFontName = formatName(fallbackFont.name)\n  const fallbackFontMetrics = capsizeFontsMetrics[fallbackFontName]\n  const fallbackFontAvgWidth =\n    fallbackFontMetrics.xWidthAvg / fallbackFontMetrics.unitsPerEm\n  let sizeAdjust = xWidthAvg ? mainFontAvgWidth / fallbackFontAvgWidth : 1\n\n  ascent = formatOverrideValue(ascent / (unitsPerEm * sizeAdjust))\n  descent = formatOverrideValue(descent / (unitsPerEm * sizeAdjust))\n  lineGap = formatOverrideValue(lineGap / (unitsPerEm * sizeAdjust))\n\n  return {\n    ascent,\n    descent,\n    lineGap,\n    fallbackFont: fallbackFont.name,\n    sizeAdjust: formatOverrideValue(sizeAdjust),\n  }\n}\n"], "names": ["calculateSizeAdjustValues", "capsizeFontsMetrics", "require", "formatName", "str", "replace", "word", "index", "toLowerCase", "toUpperCase", "formatOverrideValue", "val", "Math", "abs", "toFixed", "fontName", "font<PERSON>ey", "fontMetrics", "category", "ascent", "descent", "lineGap", "unitsPerEm", "xWidthAvg", "mainFontAvgWidth", "fallbackFont", "DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "fallback<PERSON>ontName", "name", "fallbackFontMetrics", "fallbackFontAvgWidth", "sizeAdjust"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;2BAfT;AACP,MAAMC,sBAAsBC,QAAQ;AAEpC,SAASC,WAAWC,GAAW;IAC7B,OAAOA,IACJC,OAAO,CAAC,uBAAuB,SAAUC,IAAI,EAAEC,KAAK;QACnD,OAAOA,UAAU,IAAID,KAAKE,WAAW,KAAKF,KAAKG,WAAW;IAC5D,GACCJ,OAAO,CAAC,QAAQ;AACrB;AAEA,SAASK,oBAAoBC,GAAW;IACtC,OAAOC,KAAKC,GAAG,CAACF,MAAM,KAAKG,OAAO,CAAC;AACrC;AAEO,SAASd,0BAA0Be,QAAgB;IACxD,MAAMC,UAAUb,WAAWY;IAC3B,MAAME,cAAchB,mBAAmB,CAACe,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAE,GAC/DN;IACF,MAAMO,mBAAmBD,YAAYD;IACrC,MAAMG,eACJP,aAAa,UAAUQ,6BAAkB,GAAGC,kCAAuB;IACrE,MAAMC,mBAAmBzB,WAAWsB,aAAaI,IAAI;IACrD,MAAMC,sBAAsB7B,mBAAmB,CAAC2B,iBAAiB;IACjE,MAAMG,uBACJD,oBAAoBP,SAAS,GAAGO,oBAAoBR,UAAU;IAChE,IAAIU,aAAaT,YAAYC,mBAAmBO,uBAAuB;IAEvEZ,SAAST,oBAAoBS,SAAUG,CAAAA,aAAaU,UAAS;IAC7DZ,UAAUV,oBAAoBU,UAAWE,CAAAA,aAAaU,UAAS;IAC/DX,UAAUX,oBAAoBW,UAAWC,CAAAA,aAAaU,UAAS;IAE/D,OAAO;QACLb;QACAC;QACAC;QACAI,cAAcA,aAAaI,IAAI;QAC/BG,YAAYtB,oBAAoBsB;IAClC;AACF"}
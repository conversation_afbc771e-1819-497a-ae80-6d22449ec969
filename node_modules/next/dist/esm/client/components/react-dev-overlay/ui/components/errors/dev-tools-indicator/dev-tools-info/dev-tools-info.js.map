{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.tsx"], "sourcesContent": ["import { useRef } from 'react'\nimport { MENU_DURATION_MS, useClickOutside, useFocusTrap } from '../utils'\nimport { useDelayedRender } from '../../../../hooks/use-delayed-render'\n\nexport interface DevToolsInfoPropsCore {\n  isOpen: boolean\n  triggerRef: React.RefObject<HTMLButtonElement | null>\n  close: () => void\n}\n\nexport interface DevToolsInfoProps extends DevToolsInfoPropsCore {\n  title: string\n  children: React.ReactNode\n  learnMoreLink?: string\n}\n\nexport function DevToolsInfo({\n  title,\n  children,\n  learnMoreLink,\n  isOpen,\n  triggerRef,\n  close,\n  ...props\n}: DevToolsInfoProps) {\n  const ref = useRef<HTMLDivElement | null>(null)\n  const closeButtonRef = useRef<HTMLButtonElement | null>(null)\n\n  const { mounted, rendered } = useDelayedRender(isOpen, {\n    // Intentionally no fade in, makes the UI feel more immediate\n    enterDelay: 0,\n    // Graceful fade out to confirm that the UI did not break\n    exitDelay: MENU_DURATION_MS,\n  })\n\n  useFocusTrap(ref, triggerRef, isOpen, () => {\n    // Bring focus to close button, so the user can easily close the overlay\n    closeButtonRef.current?.focus()\n  })\n  useClickOutside(ref, triggerRef, isOpen, close)\n\n  if (!mounted) {\n    return null\n  }\n\n  return (\n    <div\n      tabIndex={-1}\n      role=\"dialog\"\n      ref={ref}\n      data-info-popover\n      {...props}\n      data-rendered={rendered}\n    >\n      <div className=\"dev-tools-info-container\">\n        <h1 className=\"dev-tools-info-title\">{title}</h1>\n        {children}\n        <div className=\"dev-tools-info-button-container\">\n          <button\n            ref={closeButtonRef}\n            className=\"dev-tools-info-close-button\"\n            onClick={close}\n          >\n            Close\n          </button>\n          {learnMoreLink && (\n            <a\n              className=\"dev-tools-info-learn-more-button\"\n              href={learnMoreLink}\n              target=\"_blank\"\n              rel=\"noreferrer noopener\"\n            >\n              Learn More\n            </a>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport const DEV_TOOLS_INFO_STYLES = `\n  [data-info-popover] {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    background-clip: padding-box;\n    box-shadow: var(--shadow-menu);\n    border-radius: var(--rounded-xl);\n    position: absolute;\n    font-family: var(--font-stack-sans);\n    z-index: 1000;\n    overflow: hidden;\n    opacity: 0;\n    outline: 0;\n    min-width: 350px;\n    transition: opacity var(--animate-out-duration-ms)\n      var(--animate-out-timing-function);\n\n    &[data-rendered='true'] {\n      opacity: 1;\n      scale: 1;\n    }\n\n    button:focus-visible {\n      outline: var(--focus-ring);\n    }\n  }\n\n  .dev-tools-info-container {\n    padding: 12px;\n  }\n\n  .dev-tools-info-title {\n    padding: 8px 6px;\n    color: var(--color-gray-1000);\n    font-size: var(--size-16);\n    font-weight: 600;\n    line-height: var(--size-20);\n    margin: 0;\n  }\n\n  .dev-tools-info-article {\n    padding: 8px 6px;\n    color: var(--color-gray-1000);\n    font-size: var(--size-14);\n    line-height: var(--size-20);\n    margin: 0;\n  }\n  .dev-tools-info-paragraph {\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .dev-tools-info-button-container {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 6px;\n  }\n\n  .dev-tools-info-close-button {\n    padding: 0 8px;\n    height: var(--size-28);\n    font-size: var(--size-14);\n    font-weight: 500;\n    line-height: var(--size-20);\n    transition: background var(--duration-short) ease;\n    color: var(--color-gray-1000);\n    border-radius: var(--rounded-md-2);\n    border: 1px solid var(--color-gray-alpha-400);\n    background: var(--color-background-200);\n  }\n\n  .dev-tools-info-close-button:hover {\n    background: var(--color-gray-400);\n  }\n\n  .dev-tools-info-learn-more-button {\n    align-content: center;\n    padding: 0 8px;\n    height: var(--size-28);\n    font-size: var(--size-14);\n    font-weight: 500;\n    line-height: var(--size-20);\n    transition: background var(--duration-short) ease;\n    color: var(--color-background-100);\n    border-radius: var(--rounded-md-2);\n    background: var(--color-gray-1000);\n  }\n\n  .dev-tools-info-learn-more-button:hover {\n    text-decoration: none;\n    color: var(--color-background-100);\n    opacity: 0.9;\n  }\n`\n"], "names": ["useRef", "MENU_DURATION_MS", "useClickOutside", "useFocusTrap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DevToolsInfo", "title", "children", "learnMoreLink", "isOpen", "triggerRef", "close", "props", "ref", "closeButtonRef", "mounted", "rendered", "enterDelay", "exitDelay", "current", "focus", "div", "tabIndex", "role", "data-info-popover", "data-rendered", "className", "h1", "button", "onClick", "a", "href", "target", "rel", "DEV_TOOLS_INFO_STYLES"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,QAAO;AAC9B,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,YAAY,QAAQ,WAAU;AAC1E,SAASC,gBAAgB,QAAQ,uCAAsC;AAcvE,OAAO,SAASC,aAAa,KAQT;IARS,IAAA,EAC3BC,KAAK,EACLC,QAAQ,EACRC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,KAAK,EACL,GAAGC,OACe,GARS;IAS3B,MAAMC,MAAMb,OAA8B;IAC1C,MAAMc,iBAAiBd,OAAiC;IAExD,MAAM,EAAEe,OAAO,EAAEC,QAAQ,EAAE,GAAGZ,iBAAiBK,QAAQ;QACrD,6DAA6D;QAC7DQ,YAAY;QACZ,yDAAyD;QACzDC,WAAWjB;IACb;IAEAE,aAAaU,KAAKH,YAAYD,QAAQ;YACpC,wEAAwE;QACxEK;SAAAA,0BAAAA,eAAeK,OAAO,qBAAtBL,wBAAwBM,KAAK;IAC/B;IACAlB,gBAAgBW,KAAKH,YAAYD,QAAQE;IAEzC,IAAI,CAACI,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,KAACM;QACCC,UAAU,CAAC;QACXC,MAAK;QACLV,KAAKA;QACLW,mBAAiB;QAChB,GAAGZ,KAAK;QACTa,iBAAeT;kBAEf,cAAA,MAACK;YAAIK,WAAU;;8BACb,KAACC;oBAAGD,WAAU;8BAAwBpB;;gBACrCC;8BACD,MAACc;oBAAIK,WAAU;;sCACb,KAACE;4BACCf,KAAKC;4BACLY,WAAU;4BACVG,SAASlB;sCACV;;wBAGAH,+BACC,KAACsB;4BACCJ,WAAU;4BACVK,MAAMvB;4BACNwB,QAAO;4BACPC,KAAI;sCACL;;;;;;;AAQb;AAEA,OAAO,MAAMC,wBAAyB,i7EAmGrC"}
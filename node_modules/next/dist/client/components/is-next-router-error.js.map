{"version": 3, "sources": ["../../../src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,IAAAA,8BAAe,EAACD,UAAUE,IAAAA,6CAAyB,EAACF;AAC7D"}
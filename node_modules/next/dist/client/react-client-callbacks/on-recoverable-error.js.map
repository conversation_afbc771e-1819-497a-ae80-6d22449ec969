{"version": 3, "sources": ["../../../src/client/react-client-callbacks/on-recoverable-error.ts"], "sourcesContent": ["// This module can be shared between both pages router and app router\n\nimport type { HydrationOptions } from 'react-dom/client'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { reportGlobalError } from './report-global-error'\nimport { getReactStitchedError } from '../components/errors/stitched-error'\nimport isError from '../../lib/is-error'\n\nexport const onRecoverableError: HydrationOptions['onRecoverableError'] = (\n  error,\n  errorInfo\n) => {\n  // x-ref: https://github.com/facebook/react/pull/28736\n  const cause = isError(error) && 'cause' in error ? error.cause : error\n  const stitchedError = getReactStitchedError(cause)\n  // In development mode, pass along the component stack to the error\n  if (process.env.NODE_ENV === 'development' && errorInfo.componentStack) {\n    ;(stitchedError as any)._componentStack = errorInfo.componentStack\n  }\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(cause)) return\n\n  reportGlobalError(stitchedError)\n}\n"], "names": ["onRecoverableError", "error", "errorInfo", "cause", "isError", "stitchedError", "getReactStitchedError", "process", "env", "NODE_ENV", "componentStack", "_componentStack", "isBailoutToCSRError", "reportGlobalError"], "mappings": "AAAA,qEAAqE;;;;;+BAQxDA;;;eAAAA;;;;8BALuB;mCACF;+BACI;kEAClB;AAEb,MAAMA,qBAA6D,CACxEC,OACAC;IAEA,sDAAsD;IACtD,MAAMC,QAAQC,IAAAA,gBAAO,EAACH,UAAU,WAAWA,QAAQA,MAAME,KAAK,GAAGF;IACjE,MAAMI,gBAAgBC,IAAAA,oCAAqB,EAACH;IAC5C,mEAAmE;IACnE,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBP,UAAUQ,cAAc,EAAE;;QACpEL,cAAsBM,eAAe,GAAGT,UAAUQ,cAAc;IACpE;IACA,6EAA6E;IAC7E,IAAIE,IAAAA,iCAAmB,EAACT,QAAQ;IAEhCU,IAAAA,oCAAiB,EAACR;AACpB"}